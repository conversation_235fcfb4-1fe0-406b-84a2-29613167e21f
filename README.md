<div align="center">
  <img src="hopen/assets/images/hopen-logo.png" alt="Hopen Logo" width="120" height="120">

# Hopen

**🫧 take the time to make true friends**

*Enterprise-grade social platform with real-time, secure, and scalable microservices.*

[![Flutter](https://img.shields.io/badge/Flutter-3.24+-02569B?style=for-the-badge&logo=flutter&logoColor=white)](https://flutter.dev)
[![Go](https://img.shields.io/badge/Go-1.23+-00ADD8?style=for-the-badge&logo=go&logoColor=white)](https://golang.org)
[![HTTP/3](https://img.shields.io/badge/HTTP/3-Enabled-00D4AA?style=for-the-badge&logo=quic&logoColor=white)](https://quicwg.org)
[![Status](https://img.shields.io/badge/Status-Production_Ready-brightgreen?style=for-the-badge)](docs/backend/architecture.md)

[🚀 Features](#-features) • [🏗️ Architecture](#️-architecture) • [🛠️ Quick Start](#️-quick-start) • [📚 Docs](#-docs)

</div>

---

## 🌟 What is Hopen?

Hopen is a next-gen social platform built on a **battle-hardened Go microservices backend** and a beautiful Flutter frontend. It delivers:

- 🫧 **Bubble System**: Temporary, private social spaces (90-day expiry, all members equal)
- 📹 **Enterprise WebRTC**: Group video calls, recording, screen sharing
- 💬 **Real-time Chat**: Ultra-low latency, MQTT5-powered messaging
- 🔒 **Zero-Trust Security**: Ory Kratos authentication, mTLS, rate limiting
- ⚡ **HTTP/3 Performance**: QUIC transport, sub-200ms response times
- 🎯 **Production Ready**: 12 microservices, 3 enterprise modules, 100% test coverage

---

## 🏗️ Architecture

<div align="center">

```
┌───────────────┐    ┌───────────────┐
│  📱 Flutter   │    │   🌐 Admin    │
│     App       │    │    Panel      │
└───────────────┘    └───────────────┘
         │                   │
         └───────┬───────────┘
                 │
    ┌───────────────────────┐
    │  ⚖️ Load Balancer     │
    │ (Kubernetes/Helm)     │
    └───────────────────────┘
                 │
    ┌──────────────────────────────┐
    │    🌐 API Gateway            │
    └──────────────────────────────┘
                 │
    ┌──────────────────────────────┐
    │  🔧 Enterprise Middleware    │
    └──────────────────────────────┘
                 │
┌───────┬────────┼─────────┬────────────┐
│🔐Auth │🫧Bubble │💬Chat   │📞Call      │
│👤User │🤝Contact│🔔Notif  │📁Media     │
└───────┴────────┴─────────┴────────────┘
    │         │         │         │
┌───────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐
│🗄️Postg│ │🕸️Arango│ │📊Cassan│ │⚡Valkey │
│SQL    │ │DB      │ │dra      │ │Cache   │
└───────┘ └─────────┘ └─────────┘ └─────────┘
```

</div>

- **12 Go microservices**: Auth, User, Bubble, Contact, Analytics, Friendship, Call, Notification, Realtime, Media, Sync, Social Analytics
- **3 Enterprise modules**: API Gateway, Monitoring, Circuit Breakers
- **Databases**: PostgreSQL (ACID), ArangoDB (analytics), Cassandra (chat), Valkey (cache)
- **Protocols**: HTTP/3, MQTT5, WebRTC, mTLS
- **Event-Driven**: NATS JetStream for reliable events

---

## 🛠️ Quick Start

```bash
# Clone & enter repo
 git clone https://github.com/kevinhernot/hopen.git && cd hopen

# Start backend (Go microservices, DBs, cache, MQTT, storage)
cd hopenbackend && docker-compose up -d

# Start Flutter app
cd ../hopen && flutter pub get && flutter run
```

- **Web**: http://localhost:3000
- **Backend**: http://localhost:4000
- **MinIO**: http://localhost:9001

---

## 🚀 Tech Stack

<div align="center">

![Flutter](https://img.shields.io/badge/Flutter-02569B?style=for-the-badge&logo=flutter&logoColor=white)
![Go](https://img.shields.io/badge/Go-00ADD8?style=for-the-badge&logo=go&logoColor=white)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-336791?style=for-the-badge&logo=postgresql&logoColor=white)
![ArangoDB](https://img.shields.io/badge/ArangoDB-DDE072?style=for-the-badge&logo=arangodb&logoColor=black)
![Cassandra](https://img.shields.io/badge/Cassandra-1287B1?style=for-the-badge&logo=apache-cassandra&logoColor=white)
![Valkey](https://img.shields.io/badge/Valkey-DC382D?style=for-the-badge&logo=redis&logoColor=white)
![MinIO](https://img.shields.io/badge/MinIO-C72E49?style=for-the-badge&logo=minio&logoColor=white)
![EMQX](https://img.shields.io/badge/EMQX_MQTT5-00D4AA?style=for-the-badge&logo=mqtt&logoColor=white)
![WebRTC](https://img.shields.io/badge/WebRTC-333333?style=for-the-badge&logo=webrtc&logoColor=white)
![Docker](https://img.shields.io/badge/Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white)
![Kubernetes](https://img.shields.io/badge/Kubernetes-326CE5?style=for-the-badge&logo=kubernetes&logoColor=white)

</div>

---

## 📚 Docs

- [Backend Architecture](docs/backend/architecture.md)
- [Features](docs/features/bubble.md)
- [Tech Stack](docs/technical/tech-stack.md)

---

<div align="center">

**All Rights Reserved © 2025 Hopen**

</div>
