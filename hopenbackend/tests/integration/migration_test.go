//go:build integration
// +build integration

package integration

import (
    "context"
    "os"
    "testing"

    "github.com/jackc/pgx/v5" // assuming pgx v5
)

func TestMigrationsApplied(t *testing.T) {
    dsn := os.Getenv("PG_DSN")
    if dsn == "" {
        dsn = "postgres://hopen:hopen123@localhost:5432/hopen_db?sslmode=disable"
    }
    conn, err := pgx.Connect(context.Background(), dsn)
    if err != nil {
        t.Ski<PERSON>("cannot connect db: ", err)
    }
    defer conn.Close(context.Background())

    var count int
    if err := conn.QueryRow(context.Background(), `SELECT count(*) FROM information_schema.tables WHERE table_schema='public'`).Scan(&count); err != nil {
        t.Fatalf("query err: %v", err)
    }
    if count < 16 {
        t.<PERSON>alf("expected schema tables >=16 got %d", count)
    }
} 