package integration

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNotificationServiceEndpoints tests notification service endpoints
func TestNotificationServiceEndpoints(t *testing.T) {
	// Create a test user first
	timestamp := time.Now().Unix()
	testEmail := fmt.Sprintf("<EMAIL>", timestamp)
	testUsername := fmt.Sprintf("notifuser%d", timestamp)
	testPassword := "TestPassword123!"

	// Register user
	registerPayload := map[string]interface{}{
		"email":      testEmail,
		"username":   testUsername,
		"password":   testPassword,
		"first_name": "Notification",
		"last_name":  "Test",
	}
	
	resp, err := makeNotificationHTTPRequest("POST", "https://*********:4000/api/v1/auth/register", registerPayload, "")
	require.NoError(t, err)
	defer resp.Body.Close()
	
	var registerResult map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&registerResult)
	require.NoError(t, err)
	
	accessToken := registerResult["access_token"].(string)
	user := registerResult["user"].(map[string]interface{})
	userID := user["id"].(string)

	t.Run("Test FCM Token Registration", func(t *testing.T) {
		// Test FCM token registration
		tokenPayload := map[string]interface{}{
			"token":     "fake_fcm_token_for_testing_" + fmt.Sprint(timestamp),
			"device_id": "test_device_" + fmt.Sprint(timestamp),
			"platform":  "android",
		}

		resp, err := makeNotificationHTTPRequest("POST", "https://*********:4000/api/v1/notifications/fcm/token", tokenPayload, accessToken)
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("FCM Token Registration Response: %s", string(body))

		// Should succeed
		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Contains(t, string(body), "FCM token registered successfully")
	})

	t.Run("Test Send Push Notification", func(t *testing.T) {
		// Test sending a push notification
		notificationPayload := map[string]interface{}{
			"user_ids": []string{userID},
			"title":    "Test Notification",
			"body":     "This is a test notification",
			"data": map[string]string{
				"test_key": "test_value",
			},
		}

		resp, err := makeNotificationHTTPRequest("POST", "https://*********:4000/api/v1/notifications/push/send", notificationPayload, accessToken)
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Send Push Notification Response: %s", string(body))

		// Should return service unavailable if FCM not configured, or succeed
		assert.True(t, resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusServiceUnavailable)
	})

	t.Run("Test Get User Notifications", func(t *testing.T) {
		// Test getting user notifications
		resp, err := makeNotificationHTTPRequest("GET", "https://*********:4000/api/v1/notifications", nil, accessToken)
		require.NoError(t, err)
		defer resp.Body.Close()
		
		body, _ := io.ReadAll(resp.Body)
		t.Logf("Get Notifications Response: %s", string(body))
		
		// Should return some response (even if empty list)
		assert.True(t, resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusNotFound)
	})

	t.Run("Test Mark Notification as Read", func(t *testing.T) {
		// Test marking a notification as read (with fake but valid UUID format)
		notificationID := "550e8400-e29b-41d4-a716-************"

		resp, err := makeNotificationHTTPRequest("POST", fmt.Sprintf("https://*********:4000/api/v1/notifications/%s/read", notificationID), nil, accessToken)
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Mark as Read Response: %s", string(body))

		// Should return 404 for non-existent notification
		assert.True(t, resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusNotFound)
	})

	t.Run("Test Notification Settings", func(t *testing.T) {
		// Test getting notification settings
		resp, err := makeNotificationHTTPRequest("GET", "https://*********:4000/api/v1/notifications/settings", nil, accessToken)
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Notification Settings Response: %s", string(body))

		// Should return settings
		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Contains(t, string(body), "settings")
	})

	t.Run("Test Update Notification Settings", func(t *testing.T) {
		// Test updating notification settings (wrap in settings object)
		settingsPayload := map[string]interface{}{
			"settings": map[string]interface{}{
				"push_notifications":      true,
				"email_notifications":     false,
				"bubble_invites":          true,
				"friend_requests":         true,
				"message_notifications":   true,
			},
		}

		resp, err := makeNotificationHTTPRequest("PUT", "https://*********:4000/api/v1/notifications/settings", settingsPayload, accessToken)
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Update Settings Response: %s", string(body))

		// Should succeed
		assert.Equal(t, http.StatusOK, resp.StatusCode)
	})

	t.Run("Test Unread Count", func(t *testing.T) {
		// Test getting unread notification count
		resp, err := makeNotificationHTTPRequest("GET", "https://*********:4000/api/v1/notifications/unread-count", nil, accessToken)
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Unread Count Response: %s", string(body))

		// Should return count
		assert.Equal(t, http.StatusOK, resp.StatusCode)
	})
}

// TestNotificationServiceSecurity tests security aspects of notification service
func TestNotificationServiceSecurity(t *testing.T) {
	t.Run("Unauthorized Token Registration", func(t *testing.T) {
		// Try to register FCM token without authentication
		tokenPayload := map[string]interface{}{
			"fcm_token": "fake_token",
			"device_id": "test_device",
			"platform":  "android",
		}
		
		resp, err := makeNotificationHTTPRequest("POST", "https://*********:4000/api/v1/notifications/fcm/token", tokenPayload, "")
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Unauthorized Token Registration Response: %s", string(body))

		// Should return 401 Unauthorized
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run("Unauthorized Send Notification", func(t *testing.T) {
		// Try to send notification without authentication
		notificationPayload := map[string]interface{}{
			"recipient_id": "fake-user-id",
			"title":        "Test",
			"body":         "Test",
			"type":         "test",
		}
		
		resp, err := makeNotificationHTTPRequest("POST", "https://*********:4000/api/v1/notifications/push/send", notificationPayload, "")
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Unauthorized Send Notification Response: %s", string(body))

		// Should return 401 Unauthorized
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run("Invalid Token", func(t *testing.T) {
		// Try to access notifications with invalid token
		resp, err := makeNotificationHTTPRequest("GET", "https://*********:4000/api/v1/notifications", nil, "invalid_token")
		require.NoError(t, err)
		defer resp.Body.Close()
		
		body, _ := io.ReadAll(resp.Body)
		t.Logf("Invalid Token Response: %s", string(body))
		
		// Should return 401 Unauthorized
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})
}

// TestNotificationServiceHealth tests notification service health
func TestNotificationServiceHealth(t *testing.T) {
	t.Run("Notification Service Status", func(t *testing.T) {
		// Test if notification service is responding
		resp, err := http.Get("https://*********:4000/api/v1/notifications/health")
		if err != nil {
			// If health endpoint doesn't exist, try a basic endpoint
			resp, err = makeNotificationHTTPRequest("GET", "https://*********:4000/api/v1/notifications", nil, "fake_token")
			require.NoError(t, err)
		}
		defer resp.Body.Close()
		
		body, _ := io.ReadAll(resp.Body)
		t.Logf("Notification Service Status: %s", string(body))
		
		// Service should be responding (even if with auth errors)
		assert.True(t, resp.StatusCode < 500, "Service should be responding")
	})
}

// makeNotificationHTTPRequest is a helper function for making HTTP requests with TLS support
func makeNotificationHTTPRequest(method, url string, payload interface{}, token string) (*http.Response, error) {
	var body io.Reader
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return nil, err
		}
		body = bytes.NewBuffer(jsonData)
	}
	
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}
	
	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}
	
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   30 * time.Second,
		Transport: tr,
	}
	return client.Do(req)
}
