//go:build integration
// +build integration

package integration

import (
    "context"
    "os"
    "testing"
    "time"

    "github.com/google/uuid"
    "hopenbackend/pkg/database"
)

func TestSearchUsersReturnsPublicAccounts(t *testing.T) {
    dsn := os.Getenv("PG_DSN")
    if dsn == "" {
        dsn = "postgres://hopen:hopen123@localhost:5432/hopen_db?sslmode=disable"
    }

    dbClient, err := database.NewPostgreSQLClient(dsn, 5, 5)
    if err != nil {
        t.Fatalf("failed to connect db: %v", err)
    }
    ctx := context.Background()
    defer dbClient.Close()

    tx, err := dbClient.Pool.Begin(ctx)
    if err != nil {
        t.Fatalf("begin tx: %v", err)
    }
    defer tx.Rollback(ctx) // ensure no changes persist

    // Insert two users inside the transaction
    users := []struct {
        id, username, email, firstName string
    }{
        {uuid.NewString(), "fefelajoie", "<EMAIL>", "Felana"},
        {uuid.NewString(), "patikou", "<EMAIL>", "Patrice"},
    }

    for _, u := range users {
        if _, err := tx.Exec(ctx, `INSERT INTO users (id, username, email, first_name, is_active, is_private) VALUES ($1,$2,$3,$4,true,false)`, u.id, u.username, u.email, u.firstName); err != nil {
            t.Fatalf("insert: %v", err)
        }
    }

    // Perform the same search query inside the transaction to avoid touching committed state.
    rows, err := tx.Query(ctx, `SELECT username FROM users WHERE is_active AND is_private=false AND (username ILIKE $1 OR first_name ILIKE $1 OR last_name ILIKE $1) LIMIT 10`, "%fefela%")
    if err != nil {
        t.Fatalf("search query: %v", err)
    }
    defer rows.Close()

    var found []string
    for rows.Next() {
        var uname string
        if err := rows.Scan(&uname); err != nil {
            t.Fatalf("scan: %v", err)
        }
        found = append(found, uname)
    }

    if len(found) != 1 || found[0] != "fefelajoie" {
        t.Fatalf("expected [fefelajoie] got %v", found)
    }
}

func strPtr(s string) *string { return &s } 