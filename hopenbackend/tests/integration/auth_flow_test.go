package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	baseURL         = "https://10.0.0.81:4000"
	kratosPublicURL = "http://10.0.0.81:4433"
)

// TestCompleteAuthFlow tests the complete authentication flow
func TestCompleteAuthFlow(t *testing.T) {
	// Generate unique test data
	timestamp := time.Now().Unix()
	testEmail := fmt.Sprintf("<EMAIL>", timestamp)
	testUsername := fmt.Sprintf("testuser%d", timestamp)
	testPassword := "TestPassword123!"

	t.Run("1. Check Username Availability", func(t *testing.T) {
		payload := map[string]interface{}{
			"username": testUsername,
		}

		resp, err := makeRequest("POST", baseURL+"/api/v1/users/check-username", payload, "")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		available, ok := result["available"].(bool)
		require.True(t, ok)
		assert.True(t, available, "Username should be available")
	})

	t.Run("2. Register New User", func(t *testing.T) {
		payload := map[string]interface{}{
			"email":      testEmail,
			"username":   testUsername,
			"password":   testPassword,
			"first_name": "Test",
			"last_name":  "User",
		}

		resp, err := makeRequest("POST", baseURL+"/api/v1/auth/register", payload, "")
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Register response: %s", string(body))

		// Registration should succeed or return 409 if user already exists
		assert.True(t, resp.StatusCode == http.StatusCreated || resp.StatusCode == http.StatusConflict)
	})

	t.Run("3. Login with Email", func(t *testing.T) {
		payload := map[string]interface{}{
			"email":    testEmail,
			"password": testPassword,
		}

		resp, err := makeRequest("POST", baseURL+"/api/v1/auth/login", payload, "")
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Login response: %s", string(body))

		if resp.StatusCode == http.StatusOK {
			var result map[string]interface{}
			err = json.NewDecoder(bytes.NewReader(body)).Decode(&result)
			require.NoError(t, err)

			// Check for access token (the actual field name in response)
			accessToken, ok := result["access_token"].(string)
			require.True(t, ok, "Access token should be present")
			require.NotEmpty(t, accessToken, "Access token should not be empty")

			// Test authenticated endpoint
			t.Run("4. Test Authenticated Endpoint", func(t *testing.T) {
				resp, err := makeRequest("GET", baseURL+"/api/v1/auth/profile", nil, accessToken)
				require.NoError(t, err)
				defer resp.Body.Close()

				body, _ := io.ReadAll(resp.Body)
				t.Logf("Profile response: %s", string(body))

				assert.Equal(t, http.StatusOK, resp.StatusCode)

				var profile map[string]interface{}
				err = json.NewDecoder(bytes.NewReader(body)).Decode(&profile)
				require.NoError(t, err)

				// Verify profile data
				assert.Equal(t, testEmail, profile["email"])
				assert.Equal(t, testUsername, profile["username"])

				// Test additional authenticated endpoints
				t.Run("5. Test Additional API Endpoints", func(t *testing.T) {
					// Test user profile update
					updatePayload := map[string]interface{}{
						"first_name": "Updated",
						"last_name":  "Name",
					}

					resp, err := makeRequest("PUT", baseURL+"/api/v1/auth/profile", updatePayload, accessToken)
					require.NoError(t, err)
					defer resp.Body.Close()

					body, _ := io.ReadAll(resp.Body)
					t.Logf("Profile update response: %s", string(body))

					// Should succeed or return reasonable error
					assert.True(t, resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusBadRequest)

					// Test MQTT auth endpoint (used by EMQX)
					mqttPayload := map[string]interface{}{
						"username": profile["id"], // Use user ID as username for MQTT
						"password": accessToken,   // Use access token as password
						"clientid": "test_client_123",
						"topic":    "hopen/requests/" + profile["id"].(string),
						"action":   "subscribe",
					}

					resp, err = makeRequest("POST", baseURL+"/api/v1/auth/mqtt", mqttPayload, "")
					require.NoError(t, err)
					defer resp.Body.Close()

					body, _ = io.ReadAll(resp.Body)
					t.Logf("MQTT auth response: %s", string(body))

					// MQTT auth should work
					assert.Equal(t, http.StatusOK, resp.StatusCode)

					var mqttResult map[string]interface{}
					err = json.NewDecoder(bytes.NewReader(body)).Decode(&mqttResult)
					require.NoError(t, err)

					assert.Equal(t, "allow", mqttResult["result"])
				})
			})
		} else {
			t.Logf("Login failed with status %d, this might be expected if Kratos is still having issues", resp.StatusCode)
		}
	})
}

// TestKratosHealth tests Kratos health endpoints
func TestKratosHealth(t *testing.T) {
	t.Run("Kratos Ready", func(t *testing.T) {
		resp, err := http.Get(kratosPublicURL + "/health/ready")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		assert.Equal(t, "ok", result["status"])
	})

	t.Run("Kratos Alive", func(t *testing.T) {
		resp, err := http.Get(kratosPublicURL + "/health/alive")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		assert.Equal(t, "ok", result["status"])
	})
}
