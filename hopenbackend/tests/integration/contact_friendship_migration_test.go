package integration

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestContactFriendshipMigration tests the complete contact and friendship functionality
// after migration from ArangoDB to PostgreSQL
func TestContactFriendshipMigration(t *testing.T) {
	// Generate unique test data
	timestamp := time.Now().Unix()
	user1Email := fmt.Sprintf("<EMAIL>", timestamp)
	user2Email := fmt.Sprintf("<EMAIL>", timestamp)
	user1Username := fmt.Sprintf("user1_%d", timestamp)
	user2Username := fmt.Sprintf("user2_%d", timestamp)
	testPassword := "TestPassword123!"

	var user1Token, user2Token string
	var user1ID, user2ID string

	t.Run("1. Setup Test Users", func(t *testing.T) {
		// Register and authenticate user 1
		user1Token, user1ID = setupTestUser(t, user1Email, user1Username, testPassword)
		require.NotEmpty(t, user1Token, "User 1 token should not be empty")
		require.NotEmpty(t, user1ID, "User 1 ID should not be empty")

		// Register and authenticate user 2
		user2Token, user2ID = setupTestUser(t, user2Email, user2Username, testPassword)
		require.NotEmpty(t, user2Token, "User 2 token should not be empty")
		require.NotEmpty(t, user2ID, "User 2 ID should not be empty")

		t.Logf("User 1 ID: %s, User 2 ID: %s", user1ID, user2ID)
	})

	var contactRequestID string

	t.Run("2. Test Contact Request Creation", func(t *testing.T) {
		payload := map[string]interface{}{
			"recipient_id": user2ID,
			"message":      "Hello! Let's connect.",
		}

		resp, err := makeRequest("POST", baseURL+"/api/v1/contacts/send", payload, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusCreated, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		contact, ok := result["contact"].(map[string]interface{})
		require.True(t, ok, "Response should contain contact object")

		contactRequestID = contact["id"].(string)
		require.NotEmpty(t, contactRequestID, "Contact request ID should not be empty")

		assert.Equal(t, user1ID, contact["sender_id"])
		assert.Equal(t, user2ID, contact["receiver_id"])
		assert.Equal(t, "pending", contact["status"])
		assert.Equal(t, "Hello! Let's connect.", contact["message"])

		t.Logf("Contact request created with ID: %s", contactRequestID)
	})

	t.Run("3. Test Duplicate Contact Request Prevention", func(t *testing.T) {
		payload := map[string]interface{}{
			"recipient_id": user2ID,
			"message":      "Another request",
		}

		resp, err := makeRequest("POST", baseURL+"/api/v1/contacts/send", payload, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusConflict, resp.StatusCode)
	})

	t.Run("4. Test Get Received Contact Requests", func(t *testing.T) {
		resp, err := makeRequest("GET", baseURL+"/api/v1/contacts/received", nil, user2Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		requests, ok := result["requests"].([]interface{})
		require.True(t, ok, "Response should contain requests array")
		require.Len(t, requests, 1, "Should have exactly one received request")

		request := requests[0].(map[string]interface{})
		assert.Equal(t, contactRequestID, request["id"])
		assert.Equal(t, user1ID, request["sender_id"])
		assert.Equal(t, "pending", request["status"])
	})

	t.Run("5. Test Get Sent Contact Requests", func(t *testing.T) {
		resp, err := makeRequest("GET", baseURL+"/api/v1/contacts/sent", nil, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		requests, ok := result["requests"].([]interface{})
		require.True(t, ok, "Response should contain requests array")
		require.Len(t, requests, 1, "Should have exactly one sent request")

		request := requests[0].(map[string]interface{})
		assert.Equal(t, contactRequestID, request["id"])
		assert.Equal(t, user2ID, request["receiver_id"])
		assert.Equal(t, "pending", request["status"])
	})

	t.Run("6. Test Accept Contact Request", func(t *testing.T) {
		url := fmt.Sprintf("%s/api/v1/contacts/%s/accept", baseURL, contactRequestID)
		resp, err := makeRequest("POST", url, nil, user2Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		assert.Equal(t, "Contact request accepted successfully", result["message"])
	})

	t.Run("7. Test Get Contacts After Acceptance", func(t *testing.T) {
		// Test user 1's contacts
		resp, err := makeRequest("GET", baseURL+"/api/v1/contacts", nil, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		contacts, ok := result["contacts"].([]interface{})
		require.True(t, ok, "Response should contain contacts array")
		require.Len(t, contacts, 1, "Should have exactly one contact")

		contact := contacts[0].(map[string]interface{})
		assert.Equal(t, "accepted", contact["status"])
	})

	t.Run("8. Test Mutual Contacts", func(t *testing.T) {
		url := fmt.Sprintf("%s/api/v1/social-analytics/mutual-contacts/%s", baseURL, user2ID)
		resp, err := makeRequest("GET", url, nil, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		// Should be empty since they only have each other as contacts
		contacts, ok := result["contacts"].([]interface{})
		require.True(t, ok, "Response should contain contacts array")
		assert.Len(t, contacts, 0, "Should have no mutual contacts")
	})
}

// setupTestUser registers and authenticates a test user, returns token and user ID
func setupTestUser(t *testing.T, email, username, password string) (string, string) {
	// Check username availability
	payload := map[string]interface{}{
		"username": username,
	}
	resp, err := makeRequest("POST", baseURL+"/api/v1/users/check-username", payload, "")
	require.NoError(t, err)
	resp.Body.Close()
	require.Equal(t, http.StatusOK, resp.StatusCode)

	// Register user
	registerPayload := map[string]interface{}{
		"email":     email,
		"username":  username,
		"password":  password,
		"firstName": "Test",
		"lastName":  "User",
	}
	resp, err = makeRequest("POST", baseURL+"/api/v1/auth/register", registerPayload, "")
	require.NoError(t, err)
	resp.Body.Close()
	require.Equal(t, http.StatusCreated, resp.StatusCode)

	// Login user
	loginPayload := map[string]interface{}{
		"email":    email,
		"password": password,
	}
	resp, err = makeRequest("POST", baseURL+"/api/v1/auth/login", loginPayload, "")
	require.NoError(t, err)
	defer resp.Body.Close()
	require.Equal(t, http.StatusOK, resp.StatusCode)

	var loginResult map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&loginResult)
	require.NoError(t, err)

	token := loginResult["access_token"].(string)
	user := loginResult["user"].(map[string]interface{})
	userID := user["id"].(string)

	return token, userID
}
