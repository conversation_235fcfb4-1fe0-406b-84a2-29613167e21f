package integration

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestBubbleExpiryNATSFunctionality tests that NATS bubble expiry functionality
// is preserved after ArangoDB removal
func TestBubbleExpiryNATSFunctionality(t *testing.T) {
	// Generate unique test data
	timestamp := time.Now().Unix()
	user1Email := fmt.Sprintf("<EMAIL>", timestamp)
	user2Email := fmt.Sprintf("<EMAIL>", timestamp)
	user1Username := fmt.Sprintf("bubble_user1_%d", timestamp)
	user2Username := fmt.Sprintf("bubble_user2_%d", timestamp)
	testPassword := "TestPassword123!"

	var user1Token, user2Token string
	var user1ID, user2ID string

	t.Run("1. Setup Test Users", func(t *testing.T) {
		// Register and authenticate users
		user1Token, user1ID = setupTestUser(t, user1Email, user1Username, testPassword)
		user2Token, user2ID = setupTestUser(t, user2Email, user2Username, testPassword)

		require.NotEmpty(t, user1Token, "User 1 token should not be empty")
		require.NotEmpty(t, user2Token, "User 2 token should not be empty")

		t.Logf("Bubble test users created: %s, %s", user1ID, user2ID)
	})

	var bubbleID string

	t.Run("2. Create Bubble with Short Expiry", func(t *testing.T) {
		// Create a bubble that expires in 2 minutes for testing
		expiryTime := time.Now().Add(2 * time.Minute)
		
		payload := map[string]interface{}{
			"name":        fmt.Sprintf("Test Bubble %d", timestamp),
			"description": "Test bubble for NATS expiry functionality",
			"location": map[string]interface{}{
				"latitude":  40.7128,
				"longitude": -74.0060,
				"address":   "New York, NY",
			},
			"expires_at": expiryTime.Format(time.RFC3339),
			"max_participants": 10,
			"is_public": true,
		}

		resp, err := makeRequest("POST", baseURL+"/api/v1/bubbles", payload, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, 201, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		bubble, ok := result["bubble"].(map[string]interface{})
		require.True(t, ok, "Response should contain bubble object")

		bubbleID = bubble["id"].(string)
		require.NotEmpty(t, bubbleID, "Bubble ID should not be empty")

		t.Logf("Created bubble with ID: %s, expires at: %s", bubbleID, expiryTime.Format(time.RFC3339))
	})

	t.Run("3. Join Bubble with Both Users", func(t *testing.T) {
		// User 2 joins the bubble
		url := fmt.Sprintf("%s/api/v1/bubbles/%s/join", baseURL, bubbleID)
		resp, err := makeRequest("POST", url, nil, user2Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, 200, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		assert.Equal(t, "Successfully joined bubble", result["message"])
		t.Logf("User 2 joined bubble %s", bubbleID)
	})

	t.Run("4. Verify Bubble Membership", func(t *testing.T) {
		// Check that both users are members
		url := fmt.Sprintf("%s/api/v1/bubbles/%s", baseURL, bubbleID)
		resp, err := makeRequest("GET", url, nil, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, 200, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		bubble, ok := result["bubble"].(map[string]interface{})
		require.True(t, ok, "Response should contain bubble object")

		members, ok := bubble["members"].([]interface{})
		require.True(t, ok, "Bubble should have members array")
		assert.Len(t, members, 2, "Bubble should have 2 members")

		t.Logf("Bubble has %d members", len(members))
	})

	t.Run("5. Verify NATS Expiry Event Scheduling", func(t *testing.T) {
		// This test verifies that the bubble expiry event was scheduled in NATS
		// We can't directly test NATS from here, but we can verify the bubble
		// has the correct expiry time and status
		
		url := fmt.Sprintf("%s/api/v1/bubbles/%s", baseURL, bubbleID)
		resp, err := makeRequest("GET", url, nil, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, 200, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		bubble, ok := result["bubble"].(map[string]interface{})
		require.True(t, ok, "Response should contain bubble object")

		// Verify bubble is still active (not expired yet)
		status, ok := bubble["status"].(string)
		require.True(t, ok, "Bubble should have status")
		assert.Equal(t, "active", status, "Bubble should be active")

		// Verify expiry time is set
		expiresAt, ok := bubble["expires_at"].(string)
		require.True(t, ok, "Bubble should have expires_at")
		require.NotEmpty(t, expiresAt, "Expires at should not be empty")

		t.Logf("Bubble status: %s, expires at: %s", status, expiresAt)
	})

	t.Run("6. Test Friend Request Generation (Simulated)", func(t *testing.T) {
		// Since we can't wait for the actual expiry in a test, we'll verify
		// that the friend request endpoints work correctly
		
		// Check that no friend requests exist yet
		resp, err := makeRequest("GET", baseURL+"/api/v1/friendships/requests", nil, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, 200, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		requests, ok := result["requests"].([]interface{})
		require.True(t, ok, "Response should contain requests array")
		
		// Should be empty since bubble hasn't expired yet
		assert.Len(t, requests, 0, "Should have no friend requests yet")

		t.Logf("Friend requests before expiry: %d", len(requests))
	})

	t.Run("7. Verify Bubble Analytics Endpoints Work", func(t *testing.T) {
		// Test that social analytics endpoints work with PostgreSQL
		url := fmt.Sprintf("%s/api/v1/social-analytics/profile/%s", baseURL, user2ID)
		resp, err := makeRequest("GET", url, nil, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		// Should work even if no data yet
		assert.True(t, resp.StatusCode == 200 || resp.StatusCode == 404, 
			"Analytics endpoint should be accessible")

		if resp.StatusCode == 200 {
			var result map[string]interface{}
			err = json.NewDecoder(resp.Body).Decode(&result)
			require.NoError(t, err)
			t.Logf("Analytics response received successfully")
		}
	})

	t.Run("8. Test Bubble Listing and Search", func(t *testing.T) {
		// Verify bubble listing works
		resp, err := makeRequest("GET", baseURL+"/api/v1/bubbles", nil, user1Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, 200, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)

		bubbles, ok := result["bubbles"].([]interface{})
		require.True(t, ok, "Response should contain bubbles array")
		assert.GreaterOrEqual(t, len(bubbles), 1, "Should have at least one bubble")

		// Find our test bubble
		found := false
		for _, b := range bubbles {
			bubble := b.(map[string]interface{})
			if bubble["id"].(string) == bubbleID {
				found = true
				assert.Equal(t, "active", bubble["status"])
				break
			}
		}
		assert.True(t, found, "Test bubble should be found in listing")

		t.Logf("Bubble listing works correctly, found %d bubbles", len(bubbles))
	})

	t.Run("9. Cleanup - Leave Bubble", func(t *testing.T) {
		// User 2 leaves the bubble
		url := fmt.Sprintf("%s/api/v1/bubbles/%s/leave", baseURL, bubbleID)
		resp, err := makeRequest("POST", url, nil, user2Token)
		require.NoError(t, err)
		defer resp.Body.Close()

		// Should succeed or return appropriate status
		assert.True(t, resp.StatusCode == 200 || resp.StatusCode == 404, 
			"Leave bubble should work or bubble should not be found")

		t.Logf("User 2 left bubble (or bubble not found)")
	})
}

// Note: This test verifies that:
// 1. Bubble creation and management works
// 2. NATS expiry scheduling is set up (bubble has correct expiry time)
// 3. Friend request endpoints are functional
// 4. Social analytics endpoints work with PostgreSQL
// 5. The system is ready for actual expiry processing
//
// The actual NATS expiry processing would happen asynchronously and
// would require a longer-running test or manual verification.
