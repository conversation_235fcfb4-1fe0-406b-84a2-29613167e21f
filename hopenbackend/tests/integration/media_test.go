package integration

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	minioURL = "http://*********:9000"
	minioConsoleURL = "http://*********:9001"
)

// TestMinIOHealth tests MinIO object storage health
func TestMinIOHealth(t *testing.T) {
	t.Run("MinIO Health Check", func(t *testing.T) {
		resp, err := http.Get(minioURL + "/minio/health/live")
		require.NoError(t, err)
		defer resp.Body.Close()
		
		// MinIO health endpoint should return 200
		assert.Equal(t, http.StatusOK, resp.StatusCode)
		t.Logf("MinIO health check passed")
	})
}

// TestMediaServiceEndpoints tests media service endpoints
func TestMediaServiceEndpoints(t *testing.T) {
	// Create a test user first
	timestamp := time.Now().Unix()
	testEmail := fmt.Sprintf("<EMAIL>", timestamp)
	testUsername := fmt.Sprintf("mediauser%d", timestamp)
	testPassword := "TestPassword123!"

	// Register user
	registerPayload := map[string]interface{}{
		"email":      testEmail,
		"username":   testUsername,
		"password":   testPassword,
		"first_name": "Media",
		"last_name":  "Test",
	}
	
	resp, err := makeMediaHTTPRequest("POST", "https://*********:4000/api/v1/auth/register", registerPayload, "")
	require.NoError(t, err)
	defer resp.Body.Close()
	
	var registerResult map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&registerResult)
	require.NoError(t, err)
	
	accessToken := registerResult["access_token"].(string)

	t.Run("Test Media Upload Endpoint", func(t *testing.T) {
		// Test upload endpoint without actual file (should return error about missing file)
		var buf bytes.Buffer
		writer := multipart.NewWriter(&buf)
		writer.WriteField("test", "value")
		err = writer.Close()
		require.NoError(t, err)

		req, err := http.NewRequest("POST", "https://*********:4000/api/v1/media/upload", &buf)
		require.NoError(t, err)

		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("Authorization", "Bearer "+accessToken)

		tr := &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
		client := &http.Client{
			Timeout:   30 * time.Second,
			Transport: tr,
		}

		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Media Upload Endpoint Response: %s", string(body))

		// Should return 400 with "No file provided" error, which means endpoint is working
		assert.Equal(t, http.StatusBadRequest, resp.StatusCode)
		assert.Contains(t, string(body), "No file provided")
	})

	t.Run("Test Generate Upload URL", func(t *testing.T) {
		// Test presigned URL generation
		payload := map[string]interface{}{
			"file_name":    "test.png",
			"content_type": "image/png",
			"file_size":    1024,
		}

		resp, err := makeMediaHTTPRequest("POST", "https://*********:4000/api/v1/media/generate-upload-url", payload, accessToken)
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Generate Upload URL Response: %s", string(body))

		// Should succeed and return upload URL
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result map[string]interface{}
		err = json.NewDecoder(strings.NewReader(string(body))).Decode(&result)
		require.NoError(t, err)

		assert.Contains(t, result, "upload_url")
		assert.Contains(t, result, "file_id")
	})

	t.Run("Test Generate Download URL", func(t *testing.T) {
		// Test presigned download URL generation (should fail for non-existent file)
		payload := map[string]interface{}{
			"file_id": "non-existent-file-id",
		}

		resp, err := makeMediaHTTPRequest("POST", "https://*********:4000/api/v1/media/generate-download-url", payload, accessToken)
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("Generate Download URL Response: %s", string(body))

		// Should return 404 for non-existent file
		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
		assert.Contains(t, string(body), "File not found")
	})

	t.Run("Test User Media List", func(t *testing.T) {
		// Get user ID from register result
		user := registerResult["user"].(map[string]interface{})
		userID := user["id"].(string)

		resp, err := makeMediaHTTPRequest("GET", fmt.Sprintf("https://*********:4000/api/v1/media/user/%s", userID), nil, accessToken)
		require.NoError(t, err)
		defer resp.Body.Close()

		body, _ := io.ReadAll(resp.Body)
		t.Logf("User Media List Response: %s", string(body))

		// Should return some response (even if empty list)
		assert.True(t, resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusNotFound)
	})
}

// TestMediaServiceSecurity tests security aspects of media service
func TestMediaServiceSecurity(t *testing.T) {
	t.Run("Unauthorized Access", func(t *testing.T) {
		// Try to access media without token
		resp, err := makeMediaHTTPRequest("GET", "https://*********:4000/api/v1/media/test-file-id/info", nil, "")
		require.NoError(t, err)
		defer resp.Body.Close()
		
		body, _ := io.ReadAll(resp.Body)
		t.Logf("Unauthorized Access Response: %s", string(body))
		
		// Should return 401 Unauthorized
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run("Invalid Token", func(t *testing.T) {
		// Try to access media with invalid token
		resp, err := makeMediaHTTPRequest("GET", "https://*********:4000/api/v1/media/test-file-id/info", nil, "invalid_token")
		require.NoError(t, err)
		defer resp.Body.Close()
		
		body, _ := io.ReadAll(resp.Body)
		t.Logf("Invalid Token Response: %s", string(body))
		
		// Should return 401 Unauthorized
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})
}

// makeMediaHTTPRequest is a helper function for making HTTP requests with TLS support
func makeMediaHTTPRequest(method, url string, payload interface{}, token string) (*http.Response, error) {
	var body io.Reader
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return nil, err
		}
		body = bytes.NewBuffer(jsonData)
	}
	
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}
	
	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}
	
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   30 * time.Second,
		Transport: tr,
	}
	return client.Do(req)
}
