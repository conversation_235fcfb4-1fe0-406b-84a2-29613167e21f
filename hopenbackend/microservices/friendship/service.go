package friendship

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nats-io/nats.go"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
)

// NotificationService interface for sending notifications
type NotificationService interface {
	SendBubbleExpiredNotification(ctx context.Context, userID, bubbleID, bubbleName string, formerMembers []map[string]interface{}) error
}

// Service handles friendship operations using PostgreSQL
type Service struct {
	logger              *zap.Logger
	repository          *PostgreSQLRepository
	db                  *database.PostgreSQLClient
	config              *config.Config
	rateLimiter         *ratelimit.RateLimiter
	nats                *nats.Conn
	mqttClient          mqtt.Client
	oryClient           *ory.Client
	notificationService NotificationService
}

// Dependencies holds the dependencies for the friendship service
type Dependencies struct {
	Logger              *zap.Logger
	DB                  *database.PostgreSQLClient
	Config              *config.Config
	RateLimiter         *ratelimit.RateLimiter
	NATS                *nats.Conn
	MQTTClient          mqtt.Client
	OryClient           *ory.Client
	NotificationService NotificationService
}

// New creates a new friendship service instance
func New(deps *Dependencies) *Service {
	repository := NewPostgreSQLRepository(deps.DB.Pool, deps.Logger)

	service := &Service{
		logger:              deps.Logger,
		repository:          repository,
		db:                  deps.DB,
		config:              deps.Config,
		rateLimiter:         deps.RateLimiter,
		nats:                deps.NATS,
		mqttClient:          deps.MQTTClient,
		oryClient:           deps.OryClient,
		notificationService: deps.NotificationService,
	}

	// Subscribe to bubble expiry events
	service.subscribeToBubbleEvents()

	return service
}

// RegisterRoutes registers the friendship service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	router.GET("/requests", s.authMiddleware(), s.getFriendRequests)
	router.POST("/requests/:id/accept", s.authMiddleware(), s.acceptFriendRequest)
	router.POST("/requests/:id/decline", s.authMiddleware(), s.declineFriendRequest)
	router.GET("/friends", s.authMiddleware(), s.getFriends)
	router.DELETE("/:friendId", s.authMiddleware(), s.removeFriendship)
}

// Note: Friendship and FriendRequest structs are now defined in postgres_repository.go

// BubbleExpiredEvent represents a bubble expiry event
type BubbleExpiredEvent struct {
	BubbleID  string   `json:"bubble_id"`
	Members   []string `json:"members"`
	ExpiredAt int64    `json:"expired_at"`
}

// FriendRequestEvent represents MQTT events for friend requests
type FriendRequestEvent struct {
	EventType   string    `json:"eventType"`
	EventID     string    `json:"eventId"`
	UserID      string    `json:"userId"`
	RequestID   string    `json:"requestId"`
	Action      string    `json:"action"`
	SenderID    string    `json:"senderId"`
	ReceiverID  string    `json:"receiverId"`
	BubbleID    string    `json:"bubbleId,omitempty"`
	SenderName  string    `json:"senderName,omitempty"`
	Timestamp   time.Time `json:"timestamp"`
}

// FriendResponse represents a friend in API responses
type FriendResponse struct {
	ID             string    `json:"id"`
	UserID         string    `json:"user_id"`
	CreatedAt      time.Time `json:"created_at"`
	SourceBubbleID string    `json:"source_bubble_id"`
}

// FriendRequestResponse represents a friend request in API responses
type FriendRequestResponse struct {
	ID             string    `json:"id"`
	RequesterID    string    `json:"requester_id"`
	RecipientID    string    `json:"recipient_id"`
	Status         string    `json:"status"`
	CreatedAt      time.Time `json:"created_at"`
	SourceBubbleID string    `json:"source_bubble_id"`
	AutoGenerated  bool      `json:"auto_generated"`
}

// getFriendRequests handles getting auto-generated friend requests
func (s *Service) getFriendRequests(c *gin.Context) {
	userID, _ := c.Get("user_id")

	requests, err := s.repository.GetFriendRequestsByUser(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get friend requests",
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get friend requests"})
		return
	}

	// Convert to response format
	requestResponses := make([]*FriendRequestResponse, len(requests))
	for i, request := range requests {
		requestResponses[i] = &FriendRequestResponse{
			ID:             request.ID,
			RequesterID:    request.RequesterID,
			RecipientID:    request.RecipientID,
			Status:         request.Status,
			CreatedAt:      request.CreatedAt,
			SourceBubbleID: func() string {
				if request.SourceBubbleID != nil {
					return *request.SourceBubbleID
				}
				return ""
			}(),
			AutoGenerated:  request.AutoGenerated,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"requests": requestResponses,
		"count":    len(requestResponses),
	})
}

// acceptFriendRequest handles accepting an auto-generated friend request
func (s *Service) acceptFriendRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	// Rate limiting for friend request acceptance
	allowed, err := s.rateLimiter.AllowSocialOperation(c.Request.Context(), userID.(string), "friend_request_accept")
	if err != nil {
		s.logger.Error("Rate limit check failed", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !allowed {
		c.JSON(http.StatusTooManyRequests, gin.H{"error": "Friend request acceptance rate limit exceeded"})
		return
	}

	// Get the friend request
	request, err := s.repository.GetFriendRequestByID(c.Request.Context(), requestID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Friend request not found"})
		return
	}

	// Verify user is the recipient
	if request.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only accept requests sent to you"})
		return
	}

	// Verify request is pending
	if request.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Friend request is not pending"})
		return
	}

	// Check if both users have accepted (bidirectional requirement)
	reciprocalRequest, err := s.repository.GetReciprocalFriendRequest(c.Request.Context(), request.RequesterID, request.RecipientID)
	if err != nil {
		s.logger.Error("Failed to get reciprocal friend request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	// Update current request status
	if err := s.repository.UpdateFriendRequestStatus(c.Request.Context(), requestID, "accepted"); err != nil {
		s.logger.Error("Failed to update friend request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to accept friend request"})
		return
	}

	// Check if reciprocal request is also accepted
	if reciprocalRequest != nil && reciprocalRequest.Status == "accepted" {
		// Both users have accepted - create friendship
		friendship := &Friendship{
			User1ID:        request.RequesterID,
			User2ID:        request.RecipientID,
			SourceBubbleID: request.SourceBubbleID,
		}

		if err := s.repository.CreateFriendship(c.Request.Context(), friendship); err != nil {
			s.logger.Error("Failed to create friendship", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create friendship"})
			return
		}

		s.logger.Info("Friendship created successfully",
			zap.String("user1_id", request.RequesterID),
			zap.String("user2_id", request.RecipientID),
			zap.String("source_bubble_id", *request.SourceBubbleID))

		c.JSON(http.StatusOK, gin.H{
			"message":            "Friend request accepted and friendship created",
			"friendship_created": true,
		})
	} else {
		s.logger.Info("Friend request accepted, waiting for reciprocal acceptance",
			zap.String("request_id", requestID),
			zap.String("requester_id", request.RequesterID),
			zap.String("recipient_id", request.RecipientID))

		c.JSON(http.StatusOK, gin.H{
			"message":            "Friend request accepted, waiting for reciprocal acceptance",
			"friendship_created": false,
		})
	}
}

// declineFriendRequest handles declining an auto-generated friend request
func (s *Service) declineFriendRequest(c *gin.Context) {
	userID, _ := c.Get("user_id")
	requestID := c.Param("id")

	if requestID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Request ID is required"})
		return
	}

	// Get the friend request
	request, err := s.repository.GetFriendRequestByID(c.Request.Context(), requestID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Friend request not found"})
		return
	}

	// Verify user is the recipient
	if request.RecipientID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only decline requests sent to you"})
		return
	}

	// Verify request is pending
	if request.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Friend request is not pending"})
		return
	}

	// Update request status to declined
	if err := s.repository.UpdateFriendRequestStatus(c.Request.Context(), requestID, "declined"); err != nil {
		s.logger.Error("Failed to decline friend request", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decline friend request"})
		return
	}

	s.logger.Info("Friend request declined successfully",
		zap.String("request_id", requestID),
		zap.String("requester_id", request.RequesterID),
		zap.String("recipient_id", request.RecipientID))

	c.JSON(http.StatusOK, gin.H{"message": "Friend request declined successfully"})
}

// getFriends handles getting user's friends
func (s *Service) getFriends(c *gin.Context) {
	userID, _ := c.Get("user_id")

	friends, err := s.repository.GetFriendshipsByUser(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get friends",
			zap.String("user_id", userID.(string)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get friends"})
		return
	}

	// Convert to response format
	friendResponses := make([]*FriendResponse, len(friends))
	for i, friendship := range friends {
		// Determine the other user ID
		otherUserID := friendship.User2ID
		if friendship.User2ID == userID.(string) {
			otherUserID = friendship.User1ID
		}

		friendResponses[i] = &FriendResponse{
			ID:             friendship.ID,
			UserID:         otherUserID,
			CreatedAt:      friendship.CreatedAt,
			SourceBubbleID: func() string {
				if friendship.SourceBubbleID != nil {
					return *friendship.SourceBubbleID
				}
				return ""
			}(),
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"friends": friendResponses,
		"count":   len(friendResponses),
	})
}

// removeFriendship handles removing a friendship
func (s *Service) removeFriendship(c *gin.Context) {
	userID, _ := c.Get("user_id")
	friendshipID := c.Param("friendId")

	if friendshipID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Friendship ID is required"})
		return
	}

	// Get the friendship to verify ownership
	friendship, err := s.repository.GetFriendshipByID(c.Request.Context(), friendshipID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Friendship not found"})
		return
	}

	// Verify user is part of this friendship
	if friendship.User1ID != userID.(string) && friendship.User2ID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Cannot remove friendship you're not part of"})
		return
	}

	// Remove the friendship
	if err := s.repository.DeleteFriendship(c.Request.Context(), friendshipID); err != nil {
		s.logger.Error("Failed to remove friendship",
			zap.String("friendship_id", friendshipID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove friendship"})
		return
	}

	s.logger.Info("Friendship removed successfully",
		zap.String("friendship_id", friendshipID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Friendship removed successfully"})
}

// subscribeToBubbleEvents subscribes to bubble expiry events using JetStream
func (s *Service) subscribeToBubbleEvents() {
	if s.nats == nil {
		s.logger.Warn("NATS connection not available, cannot subscribe to bubble events")
		return
	}

	// Initialize JetStream context
	js, err := s.nats.JetStream()
	if err != nil {
		s.logger.Error("Failed to create JetStream context", zap.Error(err))
		return
	}

	// Create durable consumer for friendship generation
	consumerConfig := &nats.ConsumerConfig{
		Durable:       "friendship-generator-consumer",
		AckPolicy:     nats.AckExplicitPolicy,
		MaxDeliver:    3,
		AckWait:       30 * time.Second,
		ReplayPolicy:  nats.ReplayInstantPolicy,
		FilterSubject: "events.bubble.expired",
	}

	// Create or update consumer
	_, err = js.AddConsumer("BUBBLE_EVENTS", consumerConfig)
	if err != nil {
		s.logger.Error("Failed to create friendship generator consumer", zap.Error(err))
		return
	}

	// Subscribe with JetStream for reliable processing
	sub, err := js.PullSubscribe("events.bubble.expired", "friendship-generator-consumer")
	if err != nil {
		s.logger.Error("Failed to subscribe to bubble expiry events", zap.Error(err))
		return
	}

	s.logger.Info("Subscribed to bubble expiry events with JetStream",
		zap.String("consumer", "friendship-generator-consumer"))

	// Start processing messages
	go s.processBubbleExpiryMessages(sub)
}

// processBubbleExpiryMessages processes bubble expiry messages with JetStream
func (s *Service) processBubbleExpiryMessages(sub *nats.Subscription) {
	for {
		// Fetch messages in batches
		msgs, err := sub.Fetch(10, nats.MaxWait(5*time.Second))
		if err != nil {
			if err == nats.ErrTimeout {
				continue // No messages available
			}
			s.logger.Error("Failed to fetch bubble expiry messages", zap.Error(err))
			time.Sleep(5 * time.Second)
			continue
		}

		// Process each message
		for _, msg := range msgs {
			if err := s.handleBubbleExpiryMessage(msg); err != nil {
				s.logger.Error("Failed to process bubble expiry message", zap.Error(err))
				if err := msg.Nak(); err != nil {
					s.logger.Error("Failed to NAK message", zap.Error(err))
				}
			} else {
				if err := msg.Ack(); err != nil {
					s.logger.Error("Failed to ACK message", zap.Error(err))
				}
			}
		}
	}
}

// handleBubbleExpiryMessage handles a single bubble expiry message
func (s *Service) handleBubbleExpiryMessage(msg *nats.Msg) error {
	var event BubbleExpiredEvent
	if err := json.Unmarshal(msg.Data, &event); err != nil {
		return fmt.Errorf("failed to unmarshal bubble expiry event: %w", err)
	}

	s.logger.Info("Processing bubble expiry event",
		zap.String("bubble_id", event.BubbleID),
		zap.Strings("members", event.Members),
		zap.Int("member_count", len(event.Members)))

	// Only generate friend requests if there are 2 or more members
	if len(event.Members) < 2 {
		s.logger.Info("Bubble has less than 2 members, skipping friendship generation",
			zap.String("bubble_id", event.BubbleID))
		return nil
	}

	// Get bubble information for notifications
	ctx := context.Background()
	var bubbleName string
	err := s.db.Pool.QueryRow(ctx, `SELECT name FROM bubbles WHERE id = $1`, event.BubbleID).Scan(&bubbleName)
	if err != nil {
		s.logger.Warn("Failed to get bubble name for notifications", zap.String("bubble_id", event.BubbleID), zap.Error(err))
		bubbleName = "Unknown Bubble"
	}

	// Get member information for the friend selection dialog
	formerMembersData := make([]map[string]interface{}, 0, len(event.Members))
	for _, memberID := range event.Members {
		// Get user info for each member
		if userInfo, err := s.getUserInfo(memberID); err == nil {
			memberData := map[string]interface{}{
				"id":                 memberID,
				"user_id":            memberID,
				"username":           userInfo.Username,
				"firstName":          userInfo.FirstName,
				"first_name":         userInfo.FirstName,
				"lastName":           userInfo.LastName,
				"last_name":          userInfo.LastName,
				"profilePictureUrl":  userInfo.AvatarUrl,
				"profile_picture_url": userInfo.AvatarUrl,
				"isOnline":           false, // Default to offline
				"is_online":          false,
			}
			formerMembersData = append(formerMembersData, memberData)
		}
	}

	// Send bubble expired notifications to all former members to trigger friend selection dialog
	for _, memberID := range event.Members {
		if err := s.notificationService.SendBubbleExpiredNotification(ctx, memberID, event.BubbleID, bubbleName, formerMembersData); err != nil {
			s.logger.Warn("Failed to send bubble expired notification",
				zap.String("member_id", memberID),
				zap.String("bubble_id", event.BubbleID),
				zap.Error(err))
		}
	}

	// Generate friend requests for all member pairs
	friendRequestsCreated := 0
	for i := 0; i < len(event.Members); i++ {
		for j := i + 1; j < len(event.Members); j++ {
			user1ID := event.Members[i]
			user2ID := event.Members[j]

			// Create bidirectional friend requests
			if err := s.createAutoFriendRequest(user1ID, user2ID, event.BubbleID); err != nil {
				s.logger.Error("Failed to create friend request",
					zap.String("requester", user1ID),
					zap.String("recipient", user2ID),
					zap.String("bubble_id", event.BubbleID),
					zap.Error(err))
			} else {
				friendRequestsCreated++
			}

			if err := s.createAutoFriendRequest(user2ID, user1ID, event.BubbleID); err != nil {
				s.logger.Error("Failed to create friend request",
					zap.String("requester", user2ID),
					zap.String("recipient", user1ID),
					zap.String("bubble_id", event.BubbleID),
					zap.Error(err))
			} else {
				friendRequestsCreated++
			}
		}
	}

	s.logger.Info("Completed processing bubble expiry event",
		zap.String("bubble_id", event.BubbleID),
		zap.Int("member_count", len(event.Members)),
		zap.Int("friend_requests_created", friendRequestsCreated))

	return nil
}

// createAutoFriendRequest creates an auto-generated friend request
func (s *Service) createAutoFriendRequest(requesterID, recipientID, sourceBubbleID string) error {
	ctx := context.Background()

	// Check if friend request already exists
	exists, err := s.repository.FriendRequestExists(ctx, requesterID, recipientID, sourceBubbleID)
	if err != nil {
		return err
	}
	if exists {
		return nil // Request already exists
	}

	// Create friend request
	request := &FriendRequest{
		RequesterID:    requesterID,
		RecipientID:    recipientID,
		Status:         "pending",
		SourceBubbleID: &sourceBubbleID,
		AutoGenerated:  true,
	}

	return s.repository.CreateFriendRequest(ctx, request)
}

// Helper methods for database operations

// Note: createFriendRequest, createFriendship, and other database helper methods
// are now handled directly by the PostgreSQL repository

// Note: getFriendRequestsForUser and getFriendsForUser methods
// are now handled directly by the PostgreSQL repository

// Note: All PostgreSQL helper methods (getFriendRequestByID, getReciprocalFriendRequest,
// updateFriendRequest, friendRequestExists, getFriendshipByID, deleteFriendship)
// are now handled directly by the PostgreSQL repository

// authMiddleware provides authentication middleware
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.AuthMiddleware(s.oryClient, s.logger)
}

// UserInfo represents basic user information for notifications
type UserInfo struct {
	Username  string  `json:"username"`
	FirstName *string `json:"first_name"`
	LastName  *string `json:"last_name"`
	AvatarUrl *string `json:"avatar_url"`
}

// getUserInfo fetches user information for enhanced notifications
func (s *Service) getUserInfo(userID string) (*UserInfo, error) {
	ctx := context.Background()
	var userInfo UserInfo

	err := s.db.Pool.QueryRow(ctx, `
		SELECT username, first_name, last_name, avatar_url
		FROM users
		WHERE id = $1`, userID).Scan(
		&userInfo.Username,
		&userInfo.FirstName,
		&userInfo.LastName,
		&userInfo.AvatarUrl,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}

	return &userInfo, nil
}

// publishFriendRequestEvent publishes a friend request event to MQTT
func (s *Service) publishFriendRequestEvent(userID, senderID, requestID, action, bubbleID string) error {
	if s.mqttClient == nil || !s.mqttClient.IsConnected() {
		s.logger.Debug("MQTT client not available, skipping friend request event publishing")
		return nil
	}

	event := FriendRequestEvent{
		EventType:  "friend_request",
		EventID:    uuid.New().String(),
		UserID:     userID,
		RequestID:  requestID,
		Action:     action,
		SenderID:   senderID,
		ReceiverID: userID,
		BubbleID:   bubbleID,
		Timestamp:  time.Now(),
	}

	payload, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal friend request event: %w", err)
	}

	// Publish to unified requests topic (single source of truth)
	topic := fmt.Sprintf("hopen/requests/%s", userID)
	token := s.mqttClient.Publish(topic, 1, false, payload) // QoS 1 for reliable delivery

	if token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to publish friend request event: %w", token.Error())
	}

	s.logger.Info("Friend request event published successfully",
		zap.String("topic", topic),
		zap.String("action", action),
		zap.String("user_id", userID),
		zap.String("sender_id", senderID),
		zap.String("bubble_id", bubbleID))

	return nil
}
