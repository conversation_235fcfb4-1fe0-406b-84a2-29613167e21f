package presence

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// Service handles user presence management
type Service struct {
	redis      *redis.Client
	config     *config.Config
	logger     *zap.Logger
	mqttClient mqtt.Client
}

// PresenceStatus represents user presence status
type PresenceStatus string

const (
	StatusOnline  PresenceStatus = "online"
	StatusAway    PresenceStatus = "away"
	StatusOffline PresenceStatus = "offline"
)

// UserPresence represents user presence data
type UserPresence struct {
	UserID    string         `json:"user_id" cql:"user_id"`
	Status    PresenceStatus `json:"status" cql:"status"`
	LastSeen  time.Time      `json:"last_seen" cql:"last_seen"`
	UpdatedAt time.Time      `json:"updated_at" cql:"updated_at"`
}

// PresenceUpdate represents a presence update message
type PresenceUpdate struct {
	UserID    string         `json:"user_id"`
	Status    PresenceStatus `json:"status"`
	LastSeen  time.Time      `json:"last_seen"`
	UpdatedAt time.Time      `json:"updated_at"`
}

// NewService creates a new presence service
func NewService(redisClient *redis.Client, config *config.Config, logger *zap.Logger) *Service {
	return &Service{
		redis:  redisClient,
		config: config,
		logger: logger,
	}
}

// Initialize initializes the presence service
func (s *Service) Initialize() error {
	s.logger.Info("🔥🔥🔥 PRESENCE SERVICE INITIALIZE CALLED 🔥🔥🔥")
	s.logger.Info("Initializing presence service")

	// Initialize MQTT client
	if err := s.initializeMQTT(); err != nil {
		return fmt.Errorf("failed to initialize MQTT: %w", err)
	}

	s.logger.Info("Presence service initialized successfully")
	return nil
}

// RegisterRoutes registers the presence service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	router.GET("/users/:user_id/presence", s.getUserPresence)
	router.POST("/users/:user_id/presence", s.updateUserPresence)
	router.GET("/users/presence/batch", s.getBatchPresence)
}

// getUserPresence gets presence status for a specific user
func (s *Service) getUserPresence(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	presence, err := s.getPresenceFromRedis(userID)
	if err != nil {
		s.logger.Error("Failed to get user presence", 
			zap.String("user_id", userID), 
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get presence"})
		return
	}

	if presence == nil {
		// User not found or offline
		c.JSON(http.StatusOK, gin.H{
			"user_id": userID,
			"status":  StatusOffline,
			"last_seen": nil,
			"updated_at": time.Now(),
		})
		return
	}

	c.JSON(http.StatusOK, presence)
}

// updateUserPresence updates presence status for a user
func (s *Service) updateUserPresence(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	var update PresenceUpdate
	if err := c.ShouldBindJSON(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Ensure user_id matches
	update.UserID = userID
	update.UpdatedAt = time.Now()
	if update.Status == StatusOnline || update.Status == StatusAway {
		update.LastSeen = time.Now()
	}

	// Update in Redis
	if err := s.updatePresenceInRedis(&update); err != nil {
		s.logger.Error("Failed to update presence in Cassandra", 
			zap.String("user_id", userID), 
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update presence"})
		return
	}

	// Publish to MQTT for real-time updates
	if err := s.publishPresenceUpdate(&update); err != nil {
		s.logger.Error("Failed to publish presence update", 
			zap.String("user_id", userID), 
			zap.Error(err))
		// Don't fail the request if MQTT publish fails
	}

	c.JSON(http.StatusOK, update)
}

// getBatchPresence gets presence status for multiple users
func (s *Service) getBatchPresence(c *gin.Context) {
	userIDs := c.QueryArray("user_ids")
	if len(userIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_ids parameter is required"})
		return
	}

	if len(userIDs) > 100 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Maximum 100 user IDs allowed"})
		return
	}

	presences, err := s.getBatchPresenceFromRedis(userIDs)
	if err != nil {
		s.logger.Error("Failed to get batch presence", 
			zap.Strings("user_ids", userIDs), 
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get presence"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"presences": presences})
}

// getPresenceFromRedis retrieves presence from Redis
func (s *Service) getPresenceFromRedis(userID string) (*UserPresence, error) {
	ctx := context.Background()
	key := fmt.Sprintf("presence:%s", userID)

	// Get presence data from Redis
	data, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // User not found
		}
		return nil, fmt.Errorf("failed to get presence: %w", err)
	}

	// Parse JSON data
	var presence UserPresence
	if err := json.Unmarshal([]byte(data), &presence); err != nil {
		return nil, fmt.Errorf("failed to unmarshal presence: %w", err)
	}

	return &presence, nil
}

// updatePresenceInRedis updates presence in Redis
func (s *Service) updatePresenceInRedis(update *PresenceUpdate) error {
	ctx := context.Background()
	key := fmt.Sprintf("presence:%s", update.UserID)

	// Create presence object
	presence := UserPresence{
		UserID:    update.UserID,
		Status:    update.Status,
		LastSeen:  update.LastSeen,
		UpdatedAt: update.UpdatedAt,
	}

	// Marshal to JSON
	data, err := json.Marshal(presence)
	if err != nil {
		return fmt.Errorf("failed to marshal presence: %w", err)
	}

	// Store in Redis with TTL (1 hour)
	if err := s.redis.Set(ctx, key, data, time.Hour).Err(); err != nil {
		return fmt.Errorf("failed to update presence: %w", err)
	}

	return nil
}

// getBatchPresenceFromRedis retrieves multiple user presences
func (s *Service) getBatchPresenceFromRedis(userIDs []string) ([]UserPresence, error) {
	ctx := context.Background()
	var presences []UserPresence

	// Build Redis keys
	keys := make([]string, len(userIDs))
	for i, userID := range userIDs {
		keys[i] = fmt.Sprintf("presence:%s", userID)
	}

	// Get all presence data in one batch
	results, err := s.redis.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to query batch presence: %w", err)
	}

	// Process results
	for i, result := range results {
		userID := userIDs[i]

		if result == nil {
			// User not found, add offline status
			presences = append(presences, UserPresence{
				UserID:    userID,
				Status:    "offline",
				LastSeen:  time.Now(),
				UpdatedAt: time.Now(),
			})
		} else {
			// Parse presence data
			var presence UserPresence
			if err := json.Unmarshal([]byte(result.(string)), &presence); err != nil {
				// If unmarshal fails, treat as offline
				presences = append(presences, UserPresence{
					UserID:    userID,
					Status:    "offline",
					LastSeen:  time.Now(),
					UpdatedAt: time.Now(),
				})
			} else {
				presences = append(presences, presence)
			}
		}
	}

	return presences, nil
}

// initializeMQTT initializes the MQTT client for presence updates
func (s *Service) initializeMQTT() error {
	if s.config.MQTT.Broker == "" {
		s.logger.Warn("MQTT broker not configured, presence updates disabled")
		return nil
	}

	opts := mqtt.NewClientOptions()
	opts.AddBroker(s.config.MQTT.Broker)
	opts.SetClientID(fmt.Sprintf("presence-service-%d", time.Now().Unix()))
	opts.SetUsername(s.config.MQTT.Username)
	opts.SetPassword(s.config.MQTT.Password)
	opts.SetKeepAlive(time.Duration(s.config.MQTT.KeepAlive) * time.Second)
	opts.SetCleanSession(true)

	opts.SetOnConnectHandler(func(client mqtt.Client) {
		s.logger.Info("Presence service connected to MQTT broker")
		
		// Subscribe to global presence topic to handle incoming presence updates
		topic := "hopen/presence/global"
		token := client.Subscribe(topic, 1, s.handlePresenceMessage)
		if token.Wait() && token.Error() != nil {
			s.logger.Error("Failed to subscribe to presence topic", zap.Error(token.Error()))
		} else {
			s.logger.Info("Subscribed to presence topic", zap.String("topic", topic))
		}
	})

	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		s.logger.Error("Lost connection to MQTT broker", zap.Error(err))
	})

	s.mqttClient = mqtt.NewClient(opts)
	if token := s.mqttClient.Connect(); token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to connect to MQTT broker: %w", token.Error())
	}

	return nil
}

// handlePresenceMessage handles incoming presence messages from MQTT
func (s *Service) handlePresenceMessage(client mqtt.Client, msg mqtt.Message) {
	var update PresenceUpdate
	if err := json.Unmarshal(msg.Payload(), &update); err != nil {
		s.logger.Error("Failed to unmarshal presence message", zap.Error(err))
		return
	}

	s.logger.Debug("Received presence update", 
		zap.String("user_id", update.UserID),
		zap.String("status", string(update.Status)))

	// Update in Redis
	if err := s.updatePresenceInRedis(&update); err != nil {
		s.logger.Error("Failed to update presence from MQTT",
			zap.String("user_id", update.UserID),
			zap.Error(err))
	}

	// Broadcast to user-specific presence topics for friends/contacts
	s.broadcastPresenceToContacts(&update)
}

// publishPresenceUpdate publishes presence update to MQTT
func (s *Service) publishPresenceUpdate(update *PresenceUpdate) error {
	if s.mqttClient == nil || !s.mqttClient.IsConnected() {
		return fmt.Errorf("MQTT client not connected")
	}

	payload, err := json.Marshal(update)
	if err != nil {
		return fmt.Errorf("failed to marshal presence update: %w", err)
	}

	topic := "hopen/presence/global"
	token := s.mqttClient.Publish(topic, 0, false, payload) // QoS 0 for presence
	if token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to publish presence update: %w", token.Error())
	}

	return nil
}

// broadcastPresenceToContacts broadcasts presence to user-specific topic
// Other services can subscribe to this topic to get updates for specific users
func (s *Service) broadcastPresenceToContacts(update *PresenceUpdate) {
	// Broadcast to user's specific presence topic
	topic := fmt.Sprintf("hopen/presence/updates/%s", update.UserID)

	payload, err := json.Marshal(update)
	if err != nil {
		s.logger.Error("Failed to marshal presence update",
			zap.String("user_id", update.UserID),
			zap.Error(err))
		return
	}

	s.logger.Debug("Broadcasting presence update to user topic",
		zap.String("user_id", update.UserID),
		zap.String("topic", topic),
		zap.String("status", string(update.Status)))

	token := s.mqttClient.Publish(topic, 0, false, payload)
	if token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish presence update",
			zap.String("user_id", update.UserID),
			zap.String("topic", topic),
			zap.Error(token.Error()))
	}
}
