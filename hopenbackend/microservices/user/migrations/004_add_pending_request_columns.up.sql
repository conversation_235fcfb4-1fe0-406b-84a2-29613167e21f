-- Add pending request tracking columns to users table
ALTER TABLE users 
ADD COLUMN pending_sent_bubble_request_user_ids TEXT[] DEFAULT '{}',
ADD COLUMN pending_received_bubble_request_user_ids TEXT[] DEFAULT '{}',
ADD COLUMN pending_sent_contact_request_ids TEXT[] DEFAULT '{}',
ADD COLUMN pending_received_contact_request_ids TEXT[] DEFAULT '{}';

-- Create indexes for better query performance
CREATE INDEX idx_users_pending_sent_bubble_requests ON users USING GIN (pending_sent_bubble_request_user_ids);
CREATE INDEX idx_users_pending_received_bubble_requests ON users USING GIN (pending_received_bubble_request_user_ids);
CREATE INDEX idx_users_pending_sent_contact_requests ON users USING GIN (pending_sent_contact_request_ids);
CREATE INDEX idx_users_pending_received_contact_requests ON users USING GIN (pending_received_contact_request_ids);
