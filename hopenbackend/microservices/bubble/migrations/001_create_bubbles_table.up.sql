-- Create bubbles table - SIMPLE structure as per Flutter app
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Bubbles table - NO description, NO public/private, just basic properties
CREATE TABLE IF NOT EXISTS bubbles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    lifecycle_status VARCHAR(20) DEFAULT 'active', -- 'active', 'expired', 'archived'
    created_by VA<PERSON>HA<PERSON>(255) NOT NULL, -- References users(id)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL, -- Initial 90 days, extendable by +30 days (capped at 90 days from creation)
    max_members INTEGER DEFAULT 5
);

-- Bubble members table - SIMPLIFIED
CREATE TABLE IF NOT EXISTS bubble_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bubble_id UUID NOT NULL REFERENCES bubbles(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL, -- References users(id)
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'left', 'kicked'
    role VARCHAR(20) DEFAULT 'member', -- 'creator', 'member'
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(bubble_id, user_id)
);

-- Essential indexes only
CREATE INDEX IF NOT EXISTS idx_bubbles_created_by ON bubbles(created_by);
CREATE INDEX IF NOT EXISTS idx_bubbles_status ON bubbles(lifecycle_status);
CREATE INDEX IF NOT EXISTS idx_bubbles_expires_at ON bubbles(expires_at);
CREATE INDEX IF NOT EXISTS idx_bubbles_created_at ON bubbles(created_at);

CREATE INDEX IF NOT EXISTS idx_bubble_members_bubble_id ON bubble_members(bubble_id);
CREATE INDEX IF NOT EXISTS idx_bubble_members_user_id ON bubble_members(user_id);
CREATE INDEX IF NOT EXISTS idx_bubble_members_status ON bubble_members(status);

