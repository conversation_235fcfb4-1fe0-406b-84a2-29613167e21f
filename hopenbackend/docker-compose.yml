services:
  # PostgreSQL Database - Primary database for users, bubbles, social relationships
  postgresql:
    image: postgres:15-alpine
    container_name: hopen_postgresql
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: hopen
      POSTGRES_PASSWORD: hopen123
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hopen -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Cassandra Database - Chat database for high-performance message storage
  cassandra:
    image: cassandra:4.1
    container_name: hopen_cassandra
    ports:
      - "9042:9042"
    environment:
      CASSANDRA_CLUSTER_NAME: hopen_cluster
      CASSANDRA_DC: datacenter1
      CASSANDRA_RACK: rack1
      CASSANDRA_ENDPOINT_SNITCH: GossipingPropertyFileSnitch
      CASSANDRA_NUM_TOKENS: 128
    volumes:
      - cassandra_data:/var/lib/cassandra
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD-SHELL", "cqlsh -e 'describe cluster'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Valkey Cache - Redis-compatible cache for rate limiting
  valkey:
    image: valkey/valkey:7.2-alpine
    container_name: hopen_valkey
    ports:
      - "6379:6379"
    command: valkey-server --requirepass hopen123
    volumes:
      - valkey_data:/data
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD", "valkey-cli", "-a", "hopen123", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # NATS JetStream - Event-driven architecture
  nats:
    image: nats:2.10-alpine
    container_name: hopen_nats
    ports:
      - "4222:4222"
      - "8222:8222"
    command: ["-js", "-m", "8222"]
    volumes:
      - nats_data:/data
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: hopen_minio
    ports:
      - "${DOCKER_HOST_IP:-0.0.0.0}:9000:9000"
      - "${DOCKER_HOST_IP:-0.0.0.0}:9001:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin123}
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # EMQX MQTT5 Broker - Real-time communication (MQTT v5 ONLY)
  emqx:
    image: emqx/emqx:5.4.1
    container_name: hopen_emqx
    ports:
      - "1883:1883"   # MQTT v5 TCP
      - "8883:8883"   # MQTT v5 SSL/TLS
      - "18083:18083" # Dashboard
    environment:
      - EMQX_NAME=emqx
      - EMQX_HOST=emqx
      - EMQX_NODE__COOKIE=hopen-emqx-secure-cookie-2024
    volumes:
      - emqx_data:/opt/emqx/data
      - emqx_log:/opt/emqx/log
      - ./config/emqx.conf:/opt/emqx/etc/emqx.conf:ro
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD", "sh", "-c", "nc -z localhost 1883"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Database Migration Services
  # These services run once to initialize the databases and then exit

  # Kratos Migration Service
  kratos-migrate:
    image: oryd/kratos:v1.1.0
    container_name: hopen_kratos_migrate
    depends_on:
      postgresql:
        condition: service_healthy
    environment:
      - DSN=*****************************************/hopen?sslmode=disable
    volumes:
      - ./config/kratos:/etc/config
    command: ["migrate", "sql", "-e", "--yes", "--config", "/etc/config/kratos.yml"]
    networks:
      - hopen_network
    restart: "no"

  # Hydra Migration Service
  hydra-migrate:
    image: oryd/hydra:v2.2.0
    container_name: hopen_hydra_migrate
    depends_on:
      postgresql:
        condition: service_healthy
    environment:
      - DSN=*****************************************/hopen_hydra?sslmode=disable
    command: ["migrate", "sql", "-e", "--yes"]
    networks:
      - hopen_network
    restart: "no"

  # Backend Migration Service
  backend-migrate:
    image: migrate/migrate:v4.16.2
    container_name: hopen_backend_migrate
    depends_on:
      postgresql:
        condition: service_healthy
    volumes:
      - ./migrations/postgresql:/migrations
    command: [
      "-path", "/migrations",
      "-database", "*****************************************/hopen?sslmode=disable",
      "up"
    ]
    networks:
      - hopen_network
    restart: "no"

  # Cassandra Migration Service
  cassandra-migrate:
    image: cassandra:4.1
    container_name: hopen_cassandra_migrate
    depends_on:
      cassandra:
        condition: service_healthy
    volumes:
      - ./migrations/cassandra:/migrations
    command: [
      "cqlsh", "cassandra", "-f", "/migrations/001_initial_schema.cql"
    ]
    networks:
      - hopen_network
    restart: "no"





  # Ory Kratos (Identity Management)
  kratos:
    image: oryd/kratos:v1.1.0
    container_name: hopen_kratos
    depends_on:
      postgresql:
        condition: service_healthy
      kratos-migrate:
        condition: service_completed_successfully
    environment:
      - DSN=*****************************************/hopen?sslmode=disable
    volumes:
      - ./config/kratos:/etc/config
    command: ["serve", "--dev", "--config", "/etc/config/kratos.yml"]
    ports:
      - "4433:4433"
      - "4434:4434"
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4433/health/ready"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Ory Hydra (OAuth2/OIDC)
  hydra:
    image: oryd/hydra:v2.2.0
    container_name: hopen_hydra
    depends_on:
      postgresql:
        condition: service_healthy
      hydra-migrate:
        condition: service_completed_successfully
    environment:
      - DSN=*****************************************/hopen_hydra?sslmode=disable
      - SECRETS_SYSTEM=this-is-the-primary-secret
      - SECRETS_COOKIE=this-is-the-cookie-secret
    command: ["serve", "all", "--dev"]
    ports:
      - "4444:4444"
      - "4445:4445"
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4445/health/ready"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Hopen Backend - All 12 microservices in one Go binary
  # Includes: auth, user, bubble, contact, bubble_analytics, friendship,
  # social_analytics, call, notification, realtime, media, sync services
  backend:
    build: .
    container_name: hopen_backend
    depends_on:
      # Wait for all infrastructure to be healthy
      postgresql:
        condition: service_healthy
      cassandra:
        condition: service_healthy
      valkey:
        condition: service_healthy
      nats:
        condition: service_healthy
      minio:
        condition: service_healthy


      # Wait for auth services to be healthy
      kratos:
        condition: service_healthy
      hydra:
        condition: service_healthy

      # Wait for all migrations to complete
      kratos-migrate:
        condition: service_completed_successfully
      hydra-migrate:
        condition: service_completed_successfully
      backend-migrate:
        condition: service_completed_successfully
      cassandra-migrate:
        condition: service_completed_successfully

    ports:
      - "4000:4000"
    volumes:
      - ./config.yaml:/app/config.yaml:ro
      - ./config:/app/config:ro
      - ./certs:/app/certs:ro
    environment:
      - ENV=development
      - LOG_LEVEL=debug
    networks:
      - hopen_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "--no-check-certificate", "https://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  postgresql_data:
  cassandra_data:
  valkey_data:
  nats_data:
  minio_data:
  emqx_data:
  emqx_log:

networks:
  hopen_network:
    driver: bridge
