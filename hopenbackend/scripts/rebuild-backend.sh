#!/bin/bash

# Complete Backend Rebuild Script
# This script completely rebuilds the Hopen backend with all dependencies
# and ensures all databases are properly initialized

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}========================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}========================================${NC}\n"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found. Please run this script from the hopenbackend directory."
    exit 1
fi

print_header "🚀 HOPEN BACKEND COMPLETE REBUILD"

# Step 1: Stop and clean everything
print_status "Stopping all services and cleaning up..."
docker-compose down -v --remove-orphans || true
docker system prune -f || true

print_success "Cleanup completed"

# Step 2: Build all images
print_status "Building all Docker images (no cache)..."
docker-compose build --no-cache

print_success "Docker images built successfully"

# Step 3: Start infrastructure services first
print_status "Starting infrastructure services..."
docker-compose up -d postgresql cassandra valkey nats minio emqx

print_status "Waiting for infrastructure services to be healthy..."
# Wait for services to be healthy
for i in {1..60}; do
    if docker-compose ps | grep -E "(postgresql|cassandra|valkey|nats|minio|emqx)" | grep -q "healthy"; then
        print_success "Infrastructure services are healthy"
        break
    fi
    if [ $i -eq 60 ]; then
        print_error "Infrastructure services failed to become healthy after 5 minutes"
        docker-compose logs
        exit 1
    fi
    echo -n "."
    sleep 5
done

# Step 4: Run all migrations
print_status "Running database migrations..."
docker-compose up --no-deps kratos-migrate hydra-migrate backend-migrate cassandra-migrate

print_success "All migrations completed"

# Step 5: Start auth services
print_status "Starting authentication services..."
docker-compose up -d kratos hydra

print_status "Waiting for auth services to be healthy..."
for i in {1..30}; do
    if docker-compose ps | grep -E "(kratos|hydra)" | grep -q "healthy"; then
        print_success "Auth services are healthy"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Auth services failed to become healthy after 2.5 minutes"
        docker-compose logs kratos hydra
        exit 1
    fi
    echo -n "."
    sleep 5
done

# Step 6: Start the main backend
print_status "Starting Hopen backend service..."
docker-compose up -d backend

print_status "Waiting for backend to be healthy..."
for i in {1..30}; do
    if docker-compose ps | grep "backend" | grep -q "healthy"; then
        print_success "Backend service is healthy"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Backend service failed to become healthy after 2.5 minutes"
        docker-compose logs backend
        exit 1
    fi
    echo -n "."
    sleep 5
done

# Step 7: Verify all services
print_status "Verifying all services are running..."
docker-compose ps

print_header "✅ REBUILD COMPLETED SUCCESSFULLY"

print_success "All services are running and healthy!"
print_status "Backend API: https://10.0.0.81:4000"
print_status "MinIO Console: http://10.0.0.81:9001"
print_status "EMQX Dashboard: http://10.0.0.81:18083"
print_status "Kratos Admin: http://10.0.0.81:4434"
print_status "Hydra Admin: http://10.0.0.81:4445"

print_status "You can now test the profile picture upload functionality!"

# Step 8: Show recent logs
print_status "Recent backend logs:"
docker-compose logs --tail=20 backend

print_header "🎉 READY FOR TESTING"
