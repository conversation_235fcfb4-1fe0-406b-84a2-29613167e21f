#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create fifty diverse user profiles in the Hopen database
using the registration API endpoint.
"""

import requests
import json
import random
from datetime import datetime, timedelta
import urllib3

# Disable SSL warnings for development
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# API Configuration
BASE_URL = "https://10.0.0.81:4000/api/v1"
REGISTER_ENDPOINT = f"{BASE_URL}/auth/register"

# Sample data for generating diverse profiles
FIRST_NAMES = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"
]

<PERSON><PERSON>_<PERSON><PERSON><PERSON> = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "Lee", "Perez", "Thompson", "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson",
    "Walker", "Young", "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
    "Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell", "Carter", "Roberts",
    "Gomez", "Phillips", "Evans", "Turner", "Diaz", "Parker", "Cruz", "Edwards", "Collins", "Reyes"
]

def generate_username(first_name, last_name, index):
    """Generate a unique username"""
    base = f"{first_name.lower()}{last_name.lower()}"
    variations = [
        f"{base}{index}",
        f"{first_name.lower()}.{last_name.lower()}{index}",
        f"{first_name.lower()}_{last_name.lower()}{index}",
        f"{first_name.lower()}{index}",
        f"{base}_{index}",
    ]
    return random.choice(variations)

def generate_email(first_name, last_name, index):
    """Generate a unique email address"""
    domains = ["gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "icloud.com", "protonmail.com"]
    username_part = generate_username(first_name, last_name, index).replace("_", ".")
    return f"{username_part}@{random.choice(domains)}"

def generate_birth_date():
    """Generate a random birth date between 1980 and 2005"""
    start_date = datetime(1980, 1, 1)
    end_date = datetime(2005, 12, 31)
    time_between = end_date - start_date
    days_between = time_between.days
    random_days = random.randrange(days_between)
    birth_date = start_date + timedelta(days=random_days)
    return birth_date.strftime("%Y-%m-%d")

def create_profile(index):
    """Create a single user profile"""
    first_name = random.choice(FIRST_NAMES)
    last_name = random.choice(LAST_NAMES)
    
    profile_data = {
        "username": generate_username(first_name, last_name, index),
        "email": generate_email(first_name, last_name, index),
        "password": "TestPassword123!",  # Standard password for all test accounts
        "first_name": first_name,
        "last_name": last_name,
        "date_of_birth": generate_birth_date()
    }
    
    return profile_data

def register_user(profile_data):
    """Register a user via the API"""
    try:
        response = requests.post(
            REGISTER_ENDPOINT,
            json=profile_data,
            headers={"Content-Type": "application/json"},
            verify=False,  # Skip SSL verification for development
            timeout=30
        )
        
        if response.status_code in [200, 201]:
            print(f"✅ Created user: {profile_data['username']} ({profile_data['email']})")
            return True
        else:
            print(f"❌ Failed to create user {profile_data['username']}: {response.status_code} - {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error creating user {profile_data['username']}: {str(e)}")
        return False

def main():
    """Main function to create fifty profiles"""
    print("🚀 Starting to create fifty user profiles...")
    print(f"📡 Using API endpoint: {REGISTER_ENDPOINT}")
    print("=" * 60)
    
    successful_creations = 0
    failed_creations = 0
    
    for i in range(1, 51):  # Create 50 profiles
        print(f"\n📝 Creating profile {i}/50...")
        
        profile_data = create_profile(i)
        
        if register_user(profile_data):
            successful_creations += 1
        else:
            failed_creations += 1
        
        # Small delay to avoid overwhelming the server
        import time
        time.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print(f"✅ Successfully created: {successful_creations} profiles")
    print(f"❌ Failed to create: {failed_creations} profiles")
    print(f"📈 Success rate: {(successful_creations/50)*100:.1f}%")
    
    if successful_creations > 0:
        print(f"\n🎉 {successful_creations} new profiles are now available in the database!")
        print("💡 All test accounts use password: TestPassword123!")

if __name__ == "__main__":
    main()
