#!/bin/bash

# <PERSON>ript to apply the bubble_requests schema fix migration

set -e

echo "🔧 Applying bubble_requests schema fix migration..."

# Database connection details
DB_HOST="*********"
DB_PORT="5432"
DB_NAME="hopen_db"
DB_USER="hopen"
DB_PASSWORD="hopen123"

# Apply the migration
echo "📝 Executing migration: 002_fix_bubble_requests_schema.sql"
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "/Users/<USER>/Hopen/hopenbackend/migrations/postgresql/002_fix_bubble_requests_schema.sql"

echo "✅ Migration applied successfully!"

# Verify the schema changes
echo "🔍 Verifying schema changes..."
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
\d bubble_requests
"

echo "🎉 Schema verification complete!"
