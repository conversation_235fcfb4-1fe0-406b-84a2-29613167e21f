#!/bin/bash

# Migration Verification Test Runner
# This script runs comprehensive tests to verify the ArangoDB to PostgreSQL migration

set -e

echo "🚀 Starting ArangoDB to PostgreSQL Migration Verification Tests"
echo "=============================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "Please run this script from the hopenbackend directory"
    exit 1
fi

# Step 1: Clean up any existing containers
print_status "Cleaning up existing containers..."
docker-compose down -v --remove-orphans || true

# Step 2: Start required services
print_status "Starting PostgreSQL and NATS services..."
docker-compose up postgresql nats -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 10

# Check if PostgreSQL is ready
print_status "Checking PostgreSQL connection..."
for i in {1..30}; do
    if docker-compose exec -T postgresql pg_isready -U postgres > /dev/null 2>&1; then
        print_success "PostgreSQL is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "PostgreSQL failed to start"
        exit 1
    fi
    sleep 2
done

# Step 3: Run database migrations
print_status "Running database migrations..."
if docker-compose exec -T postgresql psql -U postgres -d hopen -c "SELECT 1;" > /dev/null 2>&1; then
    print_success "Database exists"
else
    print_status "Creating database..."
    docker-compose exec -T postgresql psql -U postgres -c "CREATE DATABASE hopen;" || true
fi

# Run migrations
print_status "Applying PostgreSQL migrations..."
for migration in migrations/postgresql/*.sql; do
    if [ -f "$migration" ]; then
        print_status "Running migration: $(basename $migration)"
        docker-compose exec -T postgresql psql -U postgres -d hopen -f "/docker-entrypoint-initdb.d/$(basename $migration)" || true
    fi
done

# Step 4: Start the backend service
print_status "Starting backend service..."
docker-compose up backend -d

# Wait for backend to be ready
print_status "Waiting for backend to be ready..."
sleep 15

# Check if backend is responding
for i in {1..30}; do
    if curl -k -s https://localhost:4000/health > /dev/null 2>&1; then
        print_success "Backend is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Backend failed to start"
        docker-compose logs backend
        exit 1
    fi
    sleep 2
done

# Step 5: Run the migration verification tests
print_status "Running contact and friendship migration tests..."
if go test -v ./tests/integration/contact_friendship_migration_test.go ./tests/integration/auth_flow_test.go -timeout 5m; then
    print_success "Contact and friendship tests passed!"
else
    print_error "Contact and friendship tests failed!"
    exit 1
fi

print_status "Running NATS bubble expiry tests..."
if go test -v ./tests/integration/bubble_expiry_nats_test.go ./tests/integration/auth_flow_test.go -timeout 5m; then
    print_success "NATS bubble expiry tests passed!"
else
    print_error "NATS bubble expiry tests failed!"
    exit 1
fi

# Step 6: Run additional verification
print_status "Running database verification queries..."
docker-compose exec -T postgresql psql -U postgres -d hopen -c "
SELECT 
    'contacts' as table_name, 
    COUNT(*) as row_count 
FROM contacts
UNION ALL
SELECT 
    'friendships' as table_name, 
    COUNT(*) as row_count 
FROM friendships
UNION ALL
SELECT 
    'friend_requests' as table_name, 
    COUNT(*) as row_count 
FROM friend_requests;
"

# Step 7: Test compilation without ArangoDB
print_status "Verifying clean compilation without ArangoDB dependencies..."
if go build -o bin/test_build ./cmd; then
    print_success "Clean compilation successful!"
    rm -f bin/test_build
else
    print_error "Compilation failed!"
    exit 1
fi

# Step 8: Check for any remaining ArangoDB references
print_status "Checking for remaining ArangoDB references..."
if grep -r -i "arangodb\|arango" --exclude-dir=.git --exclude-dir=bin --exclude="*.log" . | grep -v "REMOVED\|removed\|migration"; then
    print_warning "Found potential ArangoDB references (review above)"
else
    print_success "No remaining ArangoDB references found!"
fi

print_success "🎉 All migration verification tests passed!"
print_success "✅ ArangoDB to PostgreSQL migration is complete and verified"
print_success "✅ NATS bubble expiry functionality is preserved"
print_success "✅ Contact and friendship features work correctly"

echo ""
echo "Next steps:"
echo "1. Deploy to staging environment"
echo "2. Run load tests"
echo "3. Monitor NATS expiry events in production"
echo "4. Clean up any remaining test data"

# Cleanup
print_status "Cleaning up test environment..."
docker-compose down

print_success "Migration verification complete! 🚀"
