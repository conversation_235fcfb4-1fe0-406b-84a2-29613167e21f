-- Initialize databases for Hopen backend services
-- This script runs automatically when PostgreSQL container starts

-- Create main application database
CREATE DATABASE hopen;

-- Create Kratos database (uses main database)
-- <PERSON><PERSON><PERSON> will use the 'hopen' database

-- Create Hydra database
CREATE DATABASE hopen_hydra;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE hopen TO hopen;
GRANT ALL PRIVILEGES ON DATABASE hopen_hydra TO hopen;
