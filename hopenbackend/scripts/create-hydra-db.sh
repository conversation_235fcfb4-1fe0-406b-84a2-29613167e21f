#!/bin/bash

# Script to create Hydra database
# This script is designed to be used in Docker containers

set -e

# Configuration
POSTGRES_HOST=${POSTGRES_HOST:-postgresql}
POSTGRES_PORT=${POSTGRES_PORT:-5432}
POSTGRES_DB=${POSTGRES_DB:-hopen_db}
POSTGRES_USER=${POSTGRES_USER:-hopen}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-hopen123}
HYDRA_DB=${HYDRA_DB:-hydra_db}

echo "🔄 Creating Hydra database..."
echo "   PostgreSQL: ${POSTGRES_HOST}:${POSTGRES_PORT}"
echo "   Main DB: ${POSTGRES_DB}"
echo "   Hydra DB: ${HYDRA_DB}"

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if PGPASSWORD="${POSTGRES_PASSWORD}" psql -h "${POSTGRES_HOST}" -p "${POSTGRES_PORT}" -U "${POSTGRES_USER}" -d "${POSTGRES_DB}" -c "SELECT 1;" >/dev/null 2>&1; then
        echo "✅ PostgreSQL is ready"
        break
    fi
    echo "   Attempt $i/30: PostgreSQL not ready, waiting..."
    sleep 2
done

# Check if we can connect
if ! PGPASSWORD="${POSTGRES_PASSWORD}" psql -h "${POSTGRES_HOST}" -p "${POSTGRES_PORT}" -U "${POSTGRES_USER}" -d "${POSTGRES_DB}" -c "SELECT 1;" >/dev/null 2>&1; then
    echo "❌ Failed to connect to PostgreSQL after 60 seconds"
    exit 1
fi

# Create Hydra database if it doesn't exist
echo "📝 Creating Hydra database if it doesn't exist..."
PGPASSWORD="${POSTGRES_PASSWORD}" psql -h "${POSTGRES_HOST}" -p "${POSTGRES_PORT}" -U "${POSTGRES_USER}" -d "${POSTGRES_DB}" -c "
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = '${HYDRA_DB}') THEN
        CREATE DATABASE ${HYDRA_DB};
        RAISE NOTICE 'Database ${HYDRA_DB} created successfully';
    ELSE
        RAISE NOTICE 'Database ${HYDRA_DB} already exists';
    END IF;
END
\$\$;
"

echo "✅ Hydra database setup completed!"
