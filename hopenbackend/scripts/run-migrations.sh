#!/bin/bash

# Script to run database migrations using golang-migrate
# This script is designed to be used in Docker containers

set -e

# Configuration
POSTGRES_HOST=${POSTGRES_HOST:-postgresql}
POSTGRES_PORT=${POSTGRES_PORT:-5432}
POSTGRES_DB=${POSTGRES_DB:-hopen_db}
POSTGRES_USER=${POSTGRES_USER:-hopen}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-hopen123}
MIGRATIONS_PATH=${MIGRATIONS_PATH:-/db/migrations}

# Construct database URL
DATABASE_URL="postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?sslmode=disable"

echo "🔄 Running database migrations..."
echo "   Database: ${POSTGRES_HOST}:${POSTGRES_PORT}/${POST<PERSON>ES_DB}"
echo "   Migrations path: ${MIGRATIONS_PATH}"

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if migrate -path "${MIGRATIONS_PATH}" -database "${DATABASE_URL}" version >/dev/null 2>&1; then
        echo "✅ PostgreSQL is ready"
        break
    fi
    echo "   Attempt $i/30: PostgreSQL not ready, waiting..."
    sleep 2
done

# Check if we can connect
if ! migrate -path "${MIGRATIONS_PATH}" -database "${DATABASE_URL}" version >/dev/null 2>&1; then
    echo "❌ Failed to connect to PostgreSQL after 60 seconds"
    exit 1
fi

# Run migrations
echo "📝 Applying migrations..."
if migrate -path "${MIGRATIONS_PATH}" -database "${DATABASE_URL}" up; then
    echo "✅ Migrations completed successfully"
else
    echo "❌ Migration failed"
    exit 1
fi

# Show current migration version
CURRENT_VERSION=$(migrate -path "${MIGRATIONS_PATH}" -database "${DATABASE_URL}" version 2>/dev/null || echo "none")
echo "📊 Current migration version: ${CURRENT_VERSION}"

echo "🎉 Database migration process completed!"
