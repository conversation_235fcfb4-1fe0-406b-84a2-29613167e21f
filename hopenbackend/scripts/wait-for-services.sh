#!/bin/bash

# Wait for services to be ready before starting the application

set -e

echo "🚀 Starting Hopen Backend - Waiting for services to be ready..."

# Function to wait for a service to be ready
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=60
    local attempt=1

    echo "⏳ Waiting for $service_name at $host:$port..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to start within timeout"
    return 1
}

# Function to wait for PostgreSQL specifically
wait_for_postgres() {
    local host=$1
    local port=$2
    local database=$3
    local username=$4
    local max_attempts=60
    local attempt=1

    echo "⏳ Waiting for PostgreSQL at $host:$port..."
    
    while [ $attempt -le $max_attempts ]; do
        if PGPASSWORD="hopen123" psql -h "$host" -p "$port" -U "$username" -d "$database" -c "SELECT 1;" >/dev/null 2>&1; then
            echo "✅ PostgreSQL is ready and accessible!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - PostgreSQL not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ PostgreSQL failed to start within timeout"
    return 1
}



# Function to run database migrations
run_migrations() {
    echo "🔄 Running database migrations..."

    # PostgreSQL migrations
    if [ ! -f "/app/.migrations_done" ]; then
        echo "   Running PostgreSQL migrations..."
        for migration in /app/migrations/postgresql/*.sql; do
            if [ -f "$migration" ]; then
                echo "   Executing: $(basename "$migration")"
                PGPASSWORD="hopen123" psql -h "hopen_postgresql" -p 5432 -U "hopen" -d "hopen_db" -f "$migration"
            fi
        done
        echo "✅ PostgreSQL migrations completed!"
        touch /app/.migrations_done
    else
        echo "🔄 Migrations already applied, skipping."
    fi

    # ArangoDB setup - REMOVED (migrated to PostgreSQL)

    # Cassandra setup
    echo "   Setting up Cassandra keyspace..."
    # Run Cassandra migration
    if cqlsh hopen_cassandra -f /app/migrations/cassandra/001_initial_schema.cql; then
        echo "✅ Cassandra setup completed!"
    else
        echo "❌ Failed to setup Cassandra keyspace"
        exit 1
    fi
}

# Wait for all required services
echo "🔍 Checking service dependencies..."

# Core databases
wait_for_service "hopen_postgresql" 5432 "PostgreSQL"
# wait_for_service "hopen_arangodb" 8529 "ArangoDB" # REMOVED
wait_for_service "hopen_cassandra" 9042 "Cassandra"

# Cache and messaging
wait_for_service "hopen_valkey" 6379 "Valkey (Redis)"
wait_for_service "hopen_nats" 4222 "NATS"

# Storage and messaging
wait_for_service "hopen_minio" 9000 "MinIO"
wait_for_service "hopen_emqx" 1883 "EMQX MQTT"

# Wait specifically for PostgreSQL to be ready for connections
wait_for_postgres "hopen_postgresql" 5432 "hopen_db" "hopen"

# Run database migrations
run_migrations

echo "🎉 All services are ready! Starting Hopen Backend..."

# Start the application
exec "$@"
