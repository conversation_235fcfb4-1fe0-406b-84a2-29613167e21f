#!/bin/sh

set -e

echo "=== Hydra Initialization Script ==="
echo "Waiting for PostgreSQL to be ready..."

# Wait for PostgreSQL with timeout
timeout=60
counter=0
while ! nc -z hopen_postgresql 5432; do
    counter=$((counter + 1))
    if [ $counter -ge $timeout ]; then
        echo "ERROR: PostgreSQL did not become ready within $timeout seconds"
        exit 1
    fi
    echo "Waiting for PostgreSQL... ($counter/$timeout)"
    sleep 1
done

echo "PostgreSQL is ready!"

echo "Running Hydra database migrations..."
hydra migrate sql --yes

echo "Migrations completed successfully!"

echo "Starting Hydra server..."
exec hydra serve all 