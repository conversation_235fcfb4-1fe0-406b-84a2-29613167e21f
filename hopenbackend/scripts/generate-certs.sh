#!/bin/bash

# Generate self-signed certificates for development
# This script creates TLS certificates for testing HTTP/3 and HTTP/2

set -e

CERT_DIR="./certs"
CERT_FILE="$CERT_DIR/server.crt"
KEY_FILE="$CERT_DIR/server.key"

# Create certs directory if it doesn't exist
mkdir -p "$CERT_DIR"

echo "Generating self-signed certificate for development..."

# Generate private key
openssl genrsa -out "$KEY_FILE" 2048

# Get DOCKER_HOST_IP if available
DOCKER_HOST_IP=${DOCKER_HOST_IP:-$(ipconfig getifaddr en0 2>/dev/null || echo "*********")}

# Generate self-signed certificate directly (no CSR needed)
openssl req -x509 -new -key "$KEY_FILE" -days 365 -out "$CERT_FILE" \
    -subj "/C=US/ST=CA/L=San Francisco/O=Hopen/OU=Development/CN=$DOCKER_HOST_IP" \
    -extensions v3_req -config <(cat <<EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req

[req_distinguished_name]

[v3_req]
keyUsage = non-critical, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = hopen_backend
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = $DOCKER_HOST_IP
EOF
)

echo "Certificate generated successfully!"
echo "Certificate: $CERT_FILE"
echo "Private Key: $KEY_FILE"
echo ""
echo "To enable HTTPS with HTTP/3 support, update config.yaml:"
echo "app:"
echo "  tls:"
echo "    enabled: true"
echo "    cert_file: \"$CERT_FILE\""
echo "    key_file: \"$KEY_FILE\""
echo "    http3: true"
echo "    http2: true"
