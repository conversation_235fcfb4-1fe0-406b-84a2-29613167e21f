#!/bin/bash

# Development Certificate Setup Script for HTTP/3 Backend
# This script creates properly signed certificates for local development

set -e

echo "🔐 Setting up development certificates for HTTP/3 backend..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CERT_DIR="../certs"
DOMAIN="hopen.local"
IP="*********"
BACKEND_DIR="$(dirname "$(dirname "$(realpath "$0")")")"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if mkcert is installed
check_mkcert() {
    if ! command -v mkcert &> /dev/null; then
        print_error "mkcert is not installed!"
        echo ""
        echo "Please install mkcert first:"
        echo ""
        echo "macOS:   brew install mkcert"
        echo "Linux:   curl -JLO \"https://dl.filippo.io/mkcert/latest?for=linux/amd64\" && chmod +x mkcert-v*-linux-amd64 && sudo mv mkcert-v*-linux-amd64 /usr/local/bin/mkcert"
        echo "Windows: choco install mkcert"
        echo ""
        echo "Or visit: https://github.com/FiloSottile/mkcert"
        exit 1
    fi
}

# Install the local CA
install_ca() {
    print_status "Installing local Certificate Authority..."
    mkcert -install
    print_success "Local CA installed successfully"
}

# Create certificate directory
create_cert_dir() {
    print_status "Creating certificate directory..."
    mkdir -p "$CERT_DIR"
    print_success "Certificate directory created: $CERT_DIR"
}

# Generate certificates
generate_certificates() {
    print_status "Generating development certificates..."
    
    cd "$CERT_DIR"
    
    # Generate certificate for both domain and IP
    mkcert \
        -cert-file "hopen-dev.crt" \
        -key-file "hopen-dev.key" \
        "$DOMAIN" \
        "*.${DOMAIN}" \
        "$IP" \
        "localhost" \
        "127.0.0.1" \
        "::1"
    
    cd - > /dev/null
    
    print_success "Certificates generated successfully!"
    echo "  Certificate: $CERT_DIR/hopen-dev.crt"
    echo "  Private Key: $CERT_DIR/hopen-dev.key"
}

# Create Docker Compose override for certificates
create_docker_override() {
    print_status "Creating Docker Compose override for certificates..."
    
    cat > "$BACKEND_DIR/docker-compose.override.yml" << EOF
version: '3.8'

services:
  # Backend service with proper SSL certificates
  backend:
    volumes:
      - ./certs/hopen-dev.crt:/app/certs/server.crt:ro
      - ./certs/hopen-dev.key:/app/certs/server.key:ro
    environment:
      - SSL_CERT_PATH=/app/certs/server.crt
      - SSL_KEY_PATH=/app/certs/server.key
      - ENABLE_HTTPS=true
      - ENABLE_HTTP3=true
      - TLS_DOMAIN=$DOMAIN
    ports:
      - "8443:8443/tcp"  # HTTPS/HTTP2
      - "8443:8443/udp"  # HTTP/3 (QUIC)

  # API Gateway with proper certificates
  api-gateway:
    volumes:
      - ./certs/hopen-dev.crt:/app/certs/server.crt:ro
      - ./certs/hopen-dev.key:/app/certs/server.key:ro
    environment:
      - SSL_CERT_PATH=/app/certs/server.crt
      - SSL_KEY_PATH=/app/certs/server.key
      - ENABLE_HTTPS=true
      - ENABLE_HTTP3=true
      - TLS_DOMAIN=$DOMAIN
EOF

    print_success "Docker Compose override created"
}

# Update backend configuration
update_backend_config() {
    print_status "Updating backend configuration..."
    
    # Create TLS configuration
    cat > "$BACKEND_DIR/config/tls.yaml" << EOF
# TLS Configuration for Development
tls:
  enabled: true
  cert_file: "/app/certs/server.crt"
  key_file: "/app/certs/server.key"
  domain: "$DOMAIN"
  
  # HTTP/3 (QUIC) Configuration
  http3:
    enabled: true
    port: 8443
    
  # HTTP/2 Configuration  
  http2:
    enabled: true
    port: 8443
    
  # Security settings
  min_version: "1.2"
  max_version: "1.3"
  
  # Cipher suites optimized for HTTP/3
  cipher_suites:
    - "TLS_AES_128_GCM_SHA256"
    - "TLS_AES_256_GCM_SHA384"
    - "TLS_CHACHA20_POLY1305_SHA256"
    
  # QUIC specific settings
  quic:
    max_idle_timeout: "30s"
    max_receive_stream_flow_control_window: 6291456
    max_receive_connection_flow_control_window: 15728640
    allow_connection_migration: true
    keep_alive_period: "10s"
EOF

    print_success "Backend TLS configuration updated"
}

# Update /etc/hosts
update_hosts() {
    print_status "Updating /etc/hosts file..."
    
    # Check if entry already exists
    if grep -q "$DOMAIN" /etc/hosts; then
        print_warning "Entry for $DOMAIN already exists in /etc/hosts"
    else
        echo "Adding $DOMAIN to /etc/hosts (requires sudo)..."
        echo "$IP $DOMAIN" | sudo tee -a /etc/hosts > /dev/null
        print_success "Added $DOMAIN to /etc/hosts"
    fi
}

# Create unified environment file for development
create_env_file() {
    print_status "Creating unified environment file..."

    cat > "$BACKEND_DIR/.env" << EOF
# Hopen Backend - Unified Environment Configuration
# Single source of truth for all Docker Compose services
# Generated by setup-dev-certificates.sh

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
ENVIRONMENT=development
NODE_ENV=development
LOG_LEVEL=debug

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
DOCKER_HOST_IP=$IP
TLS_DOMAIN=$DOMAIN

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
HOPEN_APP_PORT=4000
PORT=4000

# TLS Configuration
ENABLE_HTTPS=true
ENABLE_HTTP3=true
SSL_CERT_PATH=/app/certs/server.crt
SSL_KEY_PATH=/app/certs/server.key

# API Configuration
API_BASE_URL=https://$DOMAIN:8443
BACKEND_URL=https://$DOMAIN:8443
WEBSOCKET_URL=wss://$DOMAIN:8443/ws

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MIN=120

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL (Primary Database)
POSTGRES_HOST=postgresql
POSTGRES_PORT=5432
POSTGRES_DB=hopen_db
POSTGRES_USER=hopen
POSTGRES_PASSWORD=hopen123
DATABASE_URL=*****************************************/hopen_db?sslmode=disable

# Cassandra (Message Storage)
CASSANDRA_HOSTS=cassandra:9042
CASSANDRA_KEYSPACE=hopen_messages

# =============================================================================
# CACHE AND MESSAGING
# =============================================================================
# Valkey (Redis-compatible cache)
VALKEY_HOST=valkey
VALKEY_PORT=6379
VALKEY_URL=redis://valkey:6379
VALKEY_PASSWORD=hopen123

# NATS JetStream
NATS_URL=nats://nats:4222
NATS_CONNECTION_NAME=hopen-backend-dev
NATS_ENABLE_JETSTREAM=true

# =============================================================================
# MQTT CONFIGURATION
# =============================================================================
EMQX_HOST=emqx
EMQX_PORT=1883
MQTT_BROKER_URL=emqx
MQTT_PORT=1883
MQTT_SECURE_PORT=8883
MQTT_WS_PORT=8083
MQTT_WS_URL=ws://emqx:8083/mqtt
MQTT_USERNAME=hopen_user
MQTT_PASSWORD=hopen_password

# EMQX Dashboard
EMQX_DASHBOARD__DEFAULT_USERNAME=admin
EMQX_DASHBOARD__DEFAULT_PASSWORD=hopen123

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
# MinIO Object Storage
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=hopen
MINIO_SECRET_KEY=hopen123
MINIO_BUCKET=hopen-storage
MINIO_USE_SSL=false
MINIO_ROOT_USER=hopen
MINIO_ROOT_PASSWORD=hopen123

# =============================================================================
# ORY STACK CONFIGURATION
# =============================================================================
# Kratos (Identity Management)
KRATOS_PUBLIC_URL=http://kratos:4433
KRATOS_ADMIN_URL=http://kratos:4434
KRATOS_LOG_LEVEL=info
KRATOS_CONFIG=/etc/config/kratos.yml

# Hydra (OAuth2 Server)
HYDRA_PUBLIC_URL=http://hydra:4444
HYDRA_ADMIN_URL=http://hydra:4445
HYDRA_JWKS_URL=http://hydra:4444/.well-known/jwks.json
HYDRA_AUTO_MIGRATE=true
HYDRA_LOG_LEVEL=debug
HYDRA_LOG_FORMAT=json

# Hydra Security
SECRETS_SYSTEM=hydra-secret-super-strong-change-in-production
SECRETS_COOKIE=hydra-cookie-secret-change-in-production

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# JWT Configuration
JWT_SECRET=hopen-production-jwt-secret-256-bit-key-2024-secure-token-generation
JWT_REFRESH_SECRET=hopen-production-refresh-secret-256-bit-key-2024-secure-refresh

# WebRTC Configuration
WEBRTC_HOST=webrtc-server
WEBRTC_PORT=8080
WEBRTC_STUN_PORT=3478
MINIO_BUCKET=hopen-storage
MINIO_USE_SSL=false

# Ory Stack Configuration
ORY_KRATOS_PUBLIC_URL=http://$IP:4433
ORY_KRATOS_ADMIN_URL=http://$IP:4434
ORY_HYDRA_PUBLIC_URL=http://$IP:4444
ORY_HYDRA_ADMIN_URL=http://$IP:4445
EOF

    print_success "Development environment file created"
}

# Display next steps
show_next_steps() {
    echo ""
    print_success "🎉 Backend development certificates setup complete!"
    echo ""
    echo "Next steps:"
    echo ""
    echo "1. 🐳 Restart your Docker containers:"
    echo "   cd $BACKEND_DIR"
    echo "   docker-compose down && docker-compose up -d"
    echo ""
    echo "2. 🌐 Update your Flutter app configuration to use:"
    echo "   - Domain: https://$DOMAIN:8443"
    echo "   - IP: https://$IP:8443 (fallback)"
    echo ""
    echo "3. 📱 Test HTTP/3 in your app"
    echo ""
    echo "4. 🔍 Verify certificate and HTTP/3:"
    echo "   curl -v --http3 https://$DOMAIN:8443/health"
    echo "   curl -v https://$DOMAIN:8443/health"
    echo ""
    echo "Certificate details:"
    echo "  📄 Certificate: $CERT_DIR/hopen-dev.crt"
    echo "  🔑 Private Key: $CERT_DIR/hopen-dev.key"
    echo "  🌍 Domain: $DOMAIN"
    echo "  📍 IP: $IP"
    echo ""
    echo "Configuration files created:"
    echo "  🐳 Docker Compose: $BACKEND_DIR/docker-compose.override.yml"
    echo "  🔧 TLS Config: $BACKEND_DIR/config/tls.yaml"
    echo "  🌍 Environment: $BACKEND_DIR/.env.development"
    echo ""
    print_warning "Note: Other developers will need to run this script on their machines too!"
}

# Main execution
main() {
    echo "🚀 Starting backend development certificate setup..."
    echo ""
    
    check_mkcert
    install_ca
    create_cert_dir
    generate_certificates
    create_docker_override
    update_backend_config
    update_hosts
    create_env_file
    show_next_steps
}

# Run main function
main "$@"
