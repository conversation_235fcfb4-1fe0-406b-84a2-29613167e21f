package gateway

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// APIGateway handles service discovery and intelligent routing
type APIGateway struct {
	logger   *zap.Logger
	config   *config.Config
	services map[string]*ServiceInstance
	mutex    sync.RWMutex
}

// ServiceInstance represents a registered service instance
type ServiceInstance struct {
	ID       string    `json:"id"`
	Name     string    `json:"name"`
	Host     string    `json:"host"`
	Port     int       `json:"port"`
	Health   string    `json:"health"` // healthy, unhealthy, unknown
	LastSeen time.Time `json:"last_seen"`
	Metadata map[string]string `json:"metadata"`
}

// RouteConfig defines routing configuration for services
type RouteConfig struct {
	Path        string `json:"path"`
	ServiceName string `json:"service_name"`
	StripPrefix bool   `json:"strip_prefix"`
	Timeout     int    `json:"timeout_seconds"`
}

// New creates a new API Gateway instance
func New(cfg *config.Config, logger *zap.Logger) *APIGateway {
	return &APIGateway{
		logger:   logger,
		config:   cfg,
		services: make(map[string]*ServiceInstance),
	}
}

// RegisterService registers a service instance
func (gw *APIGateway) RegisterService(service *ServiceInstance) {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	service.LastSeen = time.Now()
	gw.services[service.ID] = service

	gw.logger.Info("Service registered",
		zap.String("service_id", service.ID),
		zap.String("service_name", service.Name),
		zap.String("host", service.Host),
		zap.Int("port", service.Port))
}

// DeregisterService removes a service instance
func (gw *APIGateway) DeregisterService(serviceID string) {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	if service, exists := gw.services[serviceID]; exists {
		delete(gw.services, serviceID)
		gw.logger.Info("Service deregistered",
			zap.String("service_id", serviceID),
			zap.String("service_name", service.Name))
	}
}

// GetHealthyService returns a healthy service instance for the given service name
func (gw *APIGateway) GetHealthyService(serviceName string) (*ServiceInstance, error) {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	var healthyServices []*ServiceInstance
	for _, service := range gw.services {
		if service.Name == serviceName && service.Health == "healthy" {
			healthyServices = append(healthyServices, service)
		}
	}

	if len(healthyServices) == 0 {
		return nil, fmt.Errorf("no healthy instances found for service: %s", serviceName)
	}

	// Simple round-robin selection (could be enhanced with load balancing algorithms)
	return healthyServices[0], nil
}

// HealthCheck performs health checks on registered services
func (gw *APIGateway) HealthCheck(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			gw.performHealthChecks()
		}
	}
}

// performHealthChecks checks the health of all registered services
func (gw *APIGateway) performHealthChecks() {
	gw.mutex.Lock()
	defer gw.mutex.Unlock()

	for _, service := range gw.services {
		go gw.checkServiceHealth(service)
	}
}

// checkServiceHealth checks the health of a single service
func (gw *APIGateway) checkServiceHealth(service *ServiceInstance) {
	healthURL := fmt.Sprintf("http://%s:%d/health", service.Host, service.Port)
	
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(healthURL)
	
	gw.mutex.Lock()
	defer gw.mutex.Unlock()
	
	if err != nil || resp.StatusCode != http.StatusOK {
		service.Health = "unhealthy"
		gw.logger.Warn("Service health check failed",
			zap.String("service_id", service.ID),
			zap.String("service_name", service.Name),
			zap.Error(err))
	} else {
		service.Health = "healthy"
		service.LastSeen = time.Now()
	}
	
	if resp != nil {
		resp.Body.Close()
	}
}

// ProxyMiddleware creates a middleware for proxying requests to services
func (gw *APIGateway) ProxyMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		
		// Route mapping based on path prefixes
		var serviceName string
		switch {
		case strings.HasPrefix(path, "/api/v1/auth"):
			serviceName = "auth-service"
		case strings.HasPrefix(path, "/api/v1/users"):
			serviceName = "user-service"
		case strings.HasPrefix(path, "/api/v1/bubbles"):
			serviceName = "bubble-service"
		case strings.HasPrefix(path, "/api/v1/contact"):
			serviceName = "contact-service"
		case strings.HasPrefix(path, "/api/v1/friendship"):
			serviceName = "friendship-service"
		case strings.HasPrefix(path, "/api/v1/social"):
			serviceName = "social-analytics-service"
		case strings.HasPrefix(path, "/api/v1/calls"):
			serviceName = "call-service"
		case strings.HasPrefix(path, "/api/v1/notifications"):
			serviceName = "notification-service"
		case strings.HasPrefix(path, "/api/v1/realtime"):
			serviceName = "realtime-service"
		case strings.HasPrefix(path, "/api/v1/media"):
			serviceName = "media-service"
		default:
			c.Next()
			return
		}

		// Get healthy service instance
		service, err := gw.GetHealthyService(serviceName)
		if err != nil {
			gw.logger.Error("No healthy service instance found",
				zap.String("service_name", serviceName),
				zap.Error(err))
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error": "Service temporarily unavailable",
			})
			c.Abort()
			return
		}

		// Create reverse proxy
		target, err := url.Parse(fmt.Sprintf("http://%s:%d", service.Host, service.Port))
		if err != nil {
			gw.logger.Error("Failed to parse service URL",
				zap.String("service_name", serviceName),
				zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error",
			})
			c.Abort()
			return
		}

		proxy := httputil.NewSingleHostReverseProxy(target)
		proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
			gw.logger.Error("Proxy error",
				zap.String("service_name", serviceName),
				zap.String("target", target.String()),
				zap.Error(err))
			
			w.WriteHeader(http.StatusBadGateway)
			w.Write([]byte("Bad Gateway"))
		}

		// Add service routing headers
		c.Request.Header.Set("X-Forwarded-Service", serviceName)
		c.Request.Header.Set("X-Gateway-Request-ID", c.GetString("request_id"))

		proxy.ServeHTTP(c.Writer, c.Request)
		c.Abort()
	}
}

// GetServices returns all registered services
func (gw *APIGateway) GetServices() map[string]*ServiceInstance {
	gw.mutex.RLock()
	defer gw.mutex.RUnlock()

	services := make(map[string]*ServiceInstance)
	for k, v := range gw.services {
		services[k] = v
	}
	return services
}

// LoadBalancer interface for different load balancing strategies
type LoadBalancer interface {
	SelectService(services []*ServiceInstance) *ServiceInstance
}

// RoundRobinBalancer implements round-robin load balancing
type RoundRobinBalancer struct {
	counter int
	mutex   sync.Mutex
}

// SelectService selects a service using round-robin algorithm
func (rb *RoundRobinBalancer) SelectService(services []*ServiceInstance) *ServiceInstance {
	if len(services) == 0 {
		return nil
	}

	rb.mutex.Lock()
	defer rb.mutex.Unlock()

	service := services[rb.counter%len(services)]
	rb.counter++
	return service
}

// WeightedRoundRobinBalancer implements weighted round-robin load balancing
type WeightedRoundRobinBalancer struct {
	weights map[string]int
	current map[string]int
	mutex   sync.Mutex
}

// NewWeightedRoundRobinBalancer creates a new weighted round-robin balancer
func NewWeightedRoundRobinBalancer(weights map[string]int) *WeightedRoundRobinBalancer {
	return &WeightedRoundRobinBalancer{
		weights: weights,
		current: make(map[string]int),
	}
}

// SelectService selects a service using weighted round-robin algorithm
func (wrb *WeightedRoundRobinBalancer) SelectService(services []*ServiceInstance) *ServiceInstance {
	if len(services) == 0 {
		return nil
	}

	wrb.mutex.Lock()
	defer wrb.mutex.Unlock()

	var selected *ServiceInstance
	maxWeight := -1

	for _, service := range services {
		weight := wrb.weights[service.ID]
		if weight == 0 {
			weight = 1 // Default weight
		}

		wrb.current[service.ID] += weight
		if wrb.current[service.ID] > maxWeight {
			maxWeight = wrb.current[service.ID]
			selected = service
		}
	}

	if selected != nil {
		wrb.current[selected.ID] -= wrb.getTotalWeight(services)
	}

	return selected
}

// getTotalWeight calculates the total weight of all services
func (wrb *WeightedRoundRobinBalancer) getTotalWeight(services []*ServiceInstance) int {
	total := 0
	for _, service := range services {
		weight := wrb.weights[service.ID]
		if weight == 0 {
			weight = 1
		}
		total += weight
	}
	return total
}

// ServiceRegistry interface for service discovery
type ServiceRegistry interface {
	Register(service *ServiceInstance) error
	Deregister(serviceID string) error
	Discover(serviceName string) ([]*ServiceInstance, error)
	Watch(serviceName string) (<-chan []*ServiceInstance, error)
}

// InMemoryRegistry implements an in-memory service registry
type InMemoryRegistry struct {
	services map[string]*ServiceInstance
	watchers map[string][]chan []*ServiceInstance
	mutex    sync.RWMutex
}

// NewInMemoryRegistry creates a new in-memory service registry
func NewInMemoryRegistry() *InMemoryRegistry {
	return &InMemoryRegistry{
		services: make(map[string]*ServiceInstance),
		watchers: make(map[string][]chan []*ServiceInstance),
	}
}

// Register registers a service instance
func (r *InMemoryRegistry) Register(service *ServiceInstance) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.services[service.ID] = service
	r.notifyWatchers(service.Name)
	return nil
}

// Deregister removes a service instance
func (r *InMemoryRegistry) Deregister(serviceID string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if service, exists := r.services[serviceID]; exists {
		delete(r.services, serviceID)
		r.notifyWatchers(service.Name)
	}
	return nil
}

// Discover returns all instances of a service
func (r *InMemoryRegistry) Discover(serviceName string) ([]*ServiceInstance, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var instances []*ServiceInstance
	for _, service := range r.services {
		if service.Name == serviceName {
			instances = append(instances, service)
		}
	}
	return instances, nil
}

// Watch returns a channel that receives updates for a service
func (r *InMemoryRegistry) Watch(serviceName string) (<-chan []*ServiceInstance, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	ch := make(chan []*ServiceInstance, 1)
	r.watchers[serviceName] = append(r.watchers[serviceName], ch)

	// Send initial state
	instances, _ := r.Discover(serviceName)
	ch <- instances

	return ch, nil
}

// notifyWatchers notifies all watchers of a service about changes
func (r *InMemoryRegistry) notifyWatchers(serviceName string) {
	if watchers, exists := r.watchers[serviceName]; exists {
		instances, _ := r.Discover(serviceName)
		for _, watcher := range watchers {
			select {
			case watcher <- instances:
			default:
				// Channel is full, skip
			}
		}
	}
}
