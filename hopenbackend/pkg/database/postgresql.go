package database

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// PostgreSQLClient wraps pgxpool.Pool with additional functionality
type PostgreSQLClient struct {
	Pool   *pgxpool.Pool
	logger *zap.Logger
	config *config.PostgreSQLConfig
}

// NewPostgreSQLClient creates a new PostgreSQL client
func NewPostgreSQLClient(cfg *config.PostgreSQLConfig, logger *zap.Logger) (*PostgreSQLClient, error) {
	// Configure connection pool
	poolConfig, err := pgxpool.ParseConfig(cfg.GetDSN())
	if err != nil {
		return nil, fmt.Errorf("failed to parse PostgreSQL config: %w", err)
	}

	// Set pool configuration
	poolConfig.MaxConns = cfg.MaxConnections
	poolConfig.MinConns = cfg.MinConnections
	poolConfig.MaxConnLifetime = cfg.MaxConnectionLifetime
	poolConfig.MaxConnIdleTime = cfg.MaxConnectionIdleTime
	poolConfig.HealthCheckPeriod = cfg.HealthCheckPeriod

	// Create connection pool
	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create PostgreSQL pool: %w", err)
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping PostgreSQL: %w", err)
	}

	logger.Info("PostgreSQL connection established",
		zap.String("host", cfg.Host),
		zap.Int("port", cfg.Port),
		zap.String("database", cfg.Database),
		zap.Int32("max_connections", cfg.MaxConnections),
	)

	return &PostgreSQLClient{
		Pool:   pool,
		logger: logger,
		config: cfg,
	}, nil
}

// Close closes the PostgreSQL connection pool
func (c *PostgreSQLClient) Close() {
	if c.Pool != nil {
		c.Pool.Close()
		c.logger.Info("PostgreSQL connection pool closed")
	}
}

// Health checks the health of the PostgreSQL connection
func (c *PostgreSQLClient) Health(ctx context.Context) error {
	return c.Pool.Ping(ctx)
}

// Stats returns connection pool statistics
func (c *PostgreSQLClient) Stats() *pgxpool.Stat {
	return c.Pool.Stat()
}

// InitializeSchema initializes the database schema
func (c *PostgreSQLClient) InitializeSchema(ctx context.Context) error {
	// Create users table
	usersSchema := `
	CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
	
	CREATE TABLE IF NOT EXISTS users (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		username VARCHAR(50) UNIQUE,
		email VARCHAR(255) UNIQUE NOT NULL,
		first_name VARCHAR(100),
		last_name VARCHAR(100),
		avatar_url TEXT,
		date_of_birth DATE,
		is_active BOOLEAN DEFAULT true,
		is_private BOOLEAN DEFAULT false,
		is_banned BOOLEAN DEFAULT false,
		banned_at TIMESTAMP WITH TIME ZONE,
		notification_settings JSONB DEFAULT '{}',
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
	);`

	// Create bubbles table
	bubblesSchema := `
	CREATE TABLE IF NOT EXISTS bubbles (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		creator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
		name VARCHAR(100) NOT NULL,
		capacity INTEGER NOT NULL DEFAULT 5 CHECK (capacity >= 2 AND capacity <= 5),
		current_members INTEGER DEFAULT 1 CHECK (current_members >= 0),
		status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'archived')),
		expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
	);`

	// Create bubble_members table (ALL MEMBERS ARE EQUAL - no roles, no creator privileges)
	bubbleMembersSchema := `
	CREATE TABLE IF NOT EXISTS bubble_members (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		bubble_id UUID NOT NULL REFERENCES bubbles(id) ON DELETE CASCADE,
		user_id VARCHAR(255) NOT NULL,
		status VARCHAR(20) DEFAULT 'active',
		joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		left_at TIMESTAMP WITH TIME ZONE,
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

		UNIQUE(bubble_id, user_id)
	);`

	// Create notifications table
	notificationsSchema := `
	CREATE TABLE IF NOT EXISTS notifications (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		user_id VARCHAR(255) NOT NULL,
		type VARCHAR(50) NOT NULL,
		title VARCHAR(255) NOT NULL,
		message TEXT NOT NULL,
		data JSONB DEFAULT '{}',
		is_read BOOLEAN DEFAULT false,
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		read_at TIMESTAMP WITH TIME ZONE
	);`

	// Create media_files table
	mediaFilesSchema := `
	CREATE TABLE IF NOT EXISTS media_files (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		user_id VARCHAR(255) NOT NULL,
		filename VARCHAR(255) NOT NULL,
		original_filename VARCHAR(255) NOT NULL,
		content_type VARCHAR(100) NOT NULL,
		size_bytes BIGINT NOT NULL,
		bucket_name VARCHAR(100) NOT NULL,
		object_key VARCHAR(500) NOT NULL,
		url TEXT NOT NULL,
		metadata JSONB DEFAULT '{}',
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
	);`

	// Create call_sessions table
	callSessionsSchema := `
	CREATE TABLE IF NOT EXISTS call_sessions (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		bubble_id UUID REFERENCES bubbles(id) ON DELETE CASCADE,
		initiator_id VARCHAR(255) NOT NULL,
		participants JSONB DEFAULT '[]',
		call_type VARCHAR(20) NOT NULL,
		status VARCHAR(20) DEFAULT 'initiated',
		started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		ended_at TIMESTAMP WITH TIME ZONE,
		duration_seconds INTEGER DEFAULT 0,
		metadata JSONB DEFAULT '{}'
	);`

	// Create bubble_requests table for all bubble-related requests (invite, join, kick, start)
	bubbleRequestsSchema := `
	CREATE TABLE IF NOT EXISTS bubble_requests (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		request_type VARCHAR(20) NOT NULL, -- 'invite', 'join', 'kick', 'start'
		bubble_id UUID REFERENCES bubbles(id) ON DELETE CASCADE,
		requester_id VARCHAR(255) NOT NULL, -- User who initiated the request
		target_user_id VARCHAR(255), -- User being invited/kicked (null for join requests)
		status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'expired'
		message TEXT, -- Optional message from requester
		requires_unanimous BOOLEAN DEFAULT true, -- Whether all members must approve
		expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		completed_at TIMESTAMP WITH TIME ZONE
	);`

	// Create request_votes table for tracking individual member votes
	requestVotesSchema := `
	CREATE TABLE IF NOT EXISTS request_votes (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		request_id UUID NOT NULL REFERENCES bubble_requests(id) ON DELETE CASCADE,
		voter_id VARCHAR(255) NOT NULL, -- Member who is voting
		vote VARCHAR(10) NOT NULL, -- 'approve', 'reject'
		voted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

		UNIQUE(request_id, voter_id) -- One vote per member per request
	);`

	// Create indexes
	indexesSchema := `
	CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
	CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
	CREATE INDEX IF NOT EXISTS idx_users_is_private ON users(is_private);
	CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
	
	CREATE INDEX IF NOT EXISTS idx_bubbles_creator ON bubbles(creator_id);
	CREATE INDEX IF NOT EXISTS idx_bubbles_status ON bubbles(status);
	CREATE INDEX IF NOT EXISTS idx_bubbles_expires_at ON bubbles(expires_at);
	CREATE INDEX IF NOT EXISTS idx_bubbles_created_at ON bubbles(created_at);
	
	CREATE INDEX IF NOT EXISTS idx_bubble_members_bubble_id ON bubble_members(bubble_id);
	CREATE INDEX IF NOT EXISTS idx_bubble_members_user_id ON bubble_members(user_id);
	CREATE INDEX IF NOT EXISTS idx_bubble_members_status ON bubble_members(status);
	
	CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
	CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
	CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
	CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
	
	CREATE INDEX IF NOT EXISTS idx_media_files_user_id ON media_files(user_id);
	CREATE INDEX IF NOT EXISTS idx_media_files_content_type ON media_files(content_type);
	CREATE INDEX IF NOT EXISTS idx_media_files_created_at ON media_files(created_at);
	
	CREATE INDEX IF NOT EXISTS idx_call_sessions_bubble_id ON call_sessions(bubble_id);
	CREATE INDEX IF NOT EXISTS idx_call_sessions_initiator_id ON call_sessions(initiator_id);
	CREATE INDEX IF NOT EXISTS idx_call_sessions_status ON call_sessions(status);
	CREATE INDEX IF NOT EXISTS idx_call_sessions_started_at ON call_sessions(started_at);

	CREATE INDEX IF NOT EXISTS idx_bubble_requests_bubble_id ON bubble_requests(bubble_id);
	CREATE INDEX IF NOT EXISTS idx_bubble_requests_requester_id ON bubble_requests(requester_id);
	CREATE INDEX IF NOT EXISTS idx_bubble_requests_target_user_id ON bubble_requests(target_user_id);
	CREATE INDEX IF NOT EXISTS idx_bubble_requests_status ON bubble_requests(status);
	CREATE INDEX IF NOT EXISTS idx_bubble_requests_type ON bubble_requests(request_type);
	CREATE INDEX IF NOT EXISTS idx_bubble_requests_expires_at ON bubble_requests(expires_at);
	CREATE INDEX IF NOT EXISTS idx_bubble_requests_created_at ON bubble_requests(created_at);

	CREATE INDEX IF NOT EXISTS idx_request_votes_request_id ON request_votes(request_id);
	CREATE INDEX IF NOT EXISTS idx_request_votes_voter_id ON request_votes(voter_id);
	CREATE INDEX IF NOT EXISTS idx_request_votes_vote ON request_votes(vote);
	CREATE INDEX IF NOT EXISTS idx_request_votes_voted_at ON request_votes(voted_at);`

	// Create roles and user_roles tables for admin functionality
	rolesSchema := `
	CREATE TABLE IF NOT EXISTS roles (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		name VARCHAR(50) UNIQUE NOT NULL,
		description TEXT,
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
	);

	CREATE TABLE IF NOT EXISTS user_roles (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
		role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
		is_active BOOLEAN DEFAULT true,
		assigned_by UUID REFERENCES users(id),
		assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		UNIQUE(user_id, role_id)
	);

	-- Insert default roles
	INSERT INTO roles (name, description) VALUES
		('admin', 'System administrator with full privileges'),
		('moderator', 'Content moderator with limited admin privileges'),
		('user', 'Regular user with standard privileges')
	ON CONFLICT (name) DO NOTHING;

	CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
	CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);
	CREATE INDEX IF NOT EXISTS idx_user_roles_active ON user_roles(is_active);`

	schemas := []string{
		usersSchema,
		bubblesSchema,
		bubbleMembersSchema,
		notificationsSchema,
		mediaFilesSchema,
		callSessionsSchema,
		bubbleRequestsSchema,
		requestVotesSchema,
		rolesSchema,
		indexesSchema,
	}

	for _, schema := range schemas {
		if _, err := c.Pool.Exec(ctx, schema); err != nil {
			return fmt.Errorf("failed to execute schema: %w", err)
		}
	}

	c.logger.Info("PostgreSQL schema initialized successfully")
	return nil
}

// GetUserByID retrieves a user by ID
func (c *PostgreSQLClient) GetUserByID(ctx context.Context, userID string) (*User, error) {
	query := `
		SELECT id, username, email, first_name, last_name, avatar_url,
			   date_of_birth, is_active, is_private, is_banned, banned_at,
			   notification_settings, created_at, updated_at
		FROM users
		WHERE id = $1`

	var user User
	err := c.Pool.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
		&user.AvatarURL, &user.DateOfBirth, &user.IsActive, &user.IsPrivate,
		&user.IsBanned, &user.BannedAt, &user.NotificationSettings,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}

	// Initialize empty arrays for pending requests (these will be managed separately)
	user.PendingSentBubbleRequestUserIds = []string{}
	user.PendingReceivedBubbleRequestUserIds = []string{}
	user.PendingSentContactRequestIds = []string{}
	user.PendingReceivedContactRequestIds = []string{}

	return &user, nil
}

// CreateUser creates a new user
func (c *PostgreSQLClient) CreateUser(ctx context.Context, user *User) error {
	query := `
		INSERT INTO users (id, username, email, first_name, last_name, avatar_url,
						  date_of_birth, is_private, notification_settings)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING created_at, updated_at`

	err := c.Pool.QueryRow(ctx, query,
		user.ID, user.Username, user.Email, user.FirstName, user.LastName,
		user.AvatarURL, user.DateOfBirth, user.IsPrivate, user.NotificationSettings,
	).Scan(&user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	return nil
}

// SearchUsers searches for users by username, first name, or last name
func (c *PostgreSQLClient) SearchUsers(ctx context.Context, query string, limit int) ([]*User, error) {
	searchQuery := `
		SELECT id, username, email, first_name, last_name, avatar_url,
			   date_of_birth, is_active, is_private, notification_settings,
			   created_at, updated_at
		FROM users
		WHERE is_active = true
		AND is_private = false
		AND (username ILIKE $1 OR first_name ILIKE $1 OR last_name ILIKE $1)
		ORDER BY
			CASE
				WHEN username ILIKE $1 THEN 1
				WHEN first_name ILIKE $1 THEN 2
				WHEN last_name ILIKE $1 THEN 3
				ELSE 4
			END,
			username
		LIMIT $2`

	searchPattern := "%" + query + "%"
	rows, err := c.Pool.Query(ctx, searchQuery, searchPattern, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to search users: %w", err)
	}
	defer rows.Close()

	var users []*User
	for rows.Next() {
		var user User
		err := rows.Scan(
			&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
			&user.AvatarURL, &user.DateOfBirth, &user.IsActive, &user.IsPrivate,
			&user.NotificationSettings, &user.CreatedAt, &user.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %w", err)
		}
		users = append(users, &user)
	}

	return users, nil
}

// UpdateUser updates an existing user
func (c *PostgreSQLClient) UpdateUser(ctx context.Context, user *User) error {
	query := `
		UPDATE users
		SET username = $2, first_name = $3, last_name = $4, avatar_url = $5,
		    date_of_birth = $6, is_private = $7, notification_settings = $8,
		    pending_sent_bubble_request_user_ids = $9,
		    pending_received_bubble_request_user_ids = $10,
		    pending_sent_contact_request_ids = $11,
		    pending_received_contact_request_ids = $12,
		    updated_at = NOW()
		WHERE id = $1
		RETURNING updated_at`

	err := c.Pool.QueryRow(ctx, query,
		user.ID, user.Username, user.FirstName, user.LastName,
		user.AvatarURL, user.DateOfBirth, user.IsPrivate, user.NotificationSettings,
		user.PendingSentBubbleRequestUserIds, user.PendingReceivedBubbleRequestUserIds,
		user.PendingSentContactRequestIds, user.PendingReceivedContactRequestIds,
	).Scan(&user.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}

	return nil
}

// UpdateUserPendingRequests updates only the pending request fields for a user
func (c *PostgreSQLClient) UpdateUserPendingRequests(ctx context.Context, userID string,
	pendingSentBubbleRequestUserIds, pendingReceivedBubbleRequestUserIds,
	pendingSentContactRequestIds, pendingReceivedContactRequestIds []string) error {

	query := `
		UPDATE users
		SET pending_sent_bubble_request_user_ids = $2,
		    pending_received_bubble_request_user_ids = $3,
		    pending_sent_contact_request_ids = $4,
		    pending_received_contact_request_ids = $5,
		    updated_at = NOW()
		WHERE id = $1`

	_, err := c.Pool.Exec(ctx, query,
		userID, pendingSentBubbleRequestUserIds, pendingReceivedBubbleRequestUserIds,
		pendingSentContactRequestIds, pendingReceivedContactRequestIds)

	if err != nil {
		return fmt.Errorf("failed to update user pending requests: %w", err)
	}

	return nil
}

// AddUserPendingSentBubbleRequest adds a user ID to the pending sent bubble requests list
func (c *PostgreSQLClient) AddUserPendingSentBubbleRequest(ctx context.Context, userID, targetUserID string) error {
	query := `
		UPDATE users
		SET pending_sent_bubble_request_user_ids = array_append(
			COALESCE(pending_sent_bubble_request_user_ids, '{}'), $2
		),
		updated_at = NOW()
		WHERE id = $1 AND NOT ($2 = ANY(COALESCE(pending_sent_bubble_request_user_ids, '{}')))`

	_, err := c.Pool.Exec(ctx, query, userID, targetUserID)
	if err != nil {
		return fmt.Errorf("failed to add pending sent bubble request: %w", err)
	}
	return nil
}

// RemoveUserPendingSentBubbleRequest removes a user ID from the pending sent bubble requests list
func (c *PostgreSQLClient) RemoveUserPendingSentBubbleRequest(ctx context.Context, userID, targetUserID string) error {
	query := `
		UPDATE users
		SET pending_sent_bubble_request_user_ids = array_remove(
			COALESCE(pending_sent_bubble_request_user_ids, '{}'), $2
		),
		updated_at = NOW()
		WHERE id = $1`

	_, err := c.Pool.Exec(ctx, query, userID, targetUserID)
	if err != nil {
		return fmt.Errorf("failed to remove pending sent bubble request: %w", err)
	}
	return nil
}

// SoftDeleteUser soft deletes a user by setting is_active to false
func (c *PostgreSQLClient) SoftDeleteUser(ctx context.Context, userID string) error {
	query := `
		UPDATE users
		SET is_active = false, updated_at = NOW()
		WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to soft delete user: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// BanUser bans a user by setting is_banned to true
func (c *PostgreSQLClient) BanUser(ctx context.Context, userID string) error {
	query := `
		UPDATE users
		SET is_banned = true, banned_at = NOW(), updated_at = NOW()
		WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to ban user: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// UnbanUser unbans a user by setting is_banned to false
func (c *PostgreSQLClient) UnbanUser(ctx context.Context, userID string) error {
	query := `
		UPDATE users
		SET is_banned = false, banned_at = NULL, updated_at = NOW()
		WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to unban user: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// GetUserByEmail retrieves a user by email address
func (c *PostgreSQLClient) GetUserByEmail(ctx context.Context, email string) (*User, error) {
	query := `
		SELECT id, username, email, first_name, last_name, avatar_url, date_of_birth,
		       is_active, is_private, is_banned, banned_at, notification_settings,
		       created_at, updated_at
		FROM users
		WHERE email = $1 AND is_active = true`

	var user User
	err := c.Pool.QueryRow(ctx, query, email).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.FirstName,
		&user.LastName,
		&user.AvatarURL,
		&user.DateOfBirth,
		&user.IsActive,
		&user.IsPrivate,
		&user.IsBanned,
		&user.BannedAt,
		&user.NotificationSettings,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}

	return &user, nil
}

// GetUserByUsername retrieves a user by username
func (c *PostgreSQLClient) GetUserByUsername(ctx context.Context, username string) (*User, error) {
	query := `
		SELECT id, username, email, first_name, last_name, avatar_url, date_of_birth,
		       is_active, is_private, is_banned, banned_at, notification_settings,
		       created_at, updated_at
		FROM users
		WHERE username = $1 AND is_active = true`

	var user User
	err := c.Pool.QueryRow(ctx, query, username).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.FirstName,
		&user.LastName,
		&user.AvatarURL,
		&user.DateOfBirth,
		&user.IsActive,
		&user.IsPrivate,
		&user.IsBanned,
		&user.BannedAt,
		&user.NotificationSettings,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user by username: %w", err)
	}

	return &user, nil
}

// User represents a user in the system
type User struct {
	ID                   string                 `json:"id"`
	Username             *string                `json:"username"`
	Email                string                 `json:"email"`
	FirstName            *string                `json:"first_name"`
	LastName             *string                `json:"last_name"`
	AvatarURL            *string                `json:"avatar_url"`
	DateOfBirth          *time.Time             `json:"date_of_birth"`
	IsActive             bool                   `json:"is_active"`
	IsPrivate            bool                   `json:"is_private"`
	IsBanned             bool                   `json:"is_banned"`
	BannedAt             *time.Time             `json:"banned_at"`
	NotificationSettings map[string]interface{} `json:"notification_settings"`

	// Pending request tracking fields
	PendingSentBubbleRequestUserIds     []string `json:"pending_sent_bubble_request_user_ids"`
	PendingReceivedBubbleRequestUserIds []string `json:"pending_received_bubble_request_user_ids"`
	PendingSentContactRequestIds        []string `json:"pending_sent_contact_request_ids"`
	PendingReceivedContactRequestIds    []string `json:"pending_received_contact_request_ids"`

	CreatedAt            time.Time              `json:"created_at"`
	UpdatedAt            time.Time              `json:"updated_at"`
}

// BubbleRequest represents a bubble-related request (invite, join, kick, start)
type BubbleRequest struct {
	ID                string     `json:"id"`
	RequestType       string     `json:"request_type"`       // 'invite', 'join', 'kick', 'start'
	BubbleID          string     `json:"bubble_id"`
	RequesterID       string     `json:"requester_id"`       // User who initiated the request
	TargetUserID      *string    `json:"target_user_id"`     // User being invited/kicked (null for join)
	Status            string     `json:"status"`             // 'pending', 'approved', 'rejected', 'expired'
	Message           *string    `json:"message"`            // Optional message from requester
	RequiresUnanimous bool       `json:"requires_unanimous"` // Whether all members must approve
	ExpiresAt         time.Time  `json:"expires_at"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
	CompletedAt       *time.Time `json:"completed_at"`
}

// RequestVote represents an individual member's vote on a request
type RequestVote struct {
	ID        string    `json:"id"`
	RequestID string    `json:"request_id"`
	VoterID   string    `json:"voter_id"` // Member who is voting
	Vote      string    `json:"vote"`     // 'approve', 'reject'
	VotedAt   time.Time `json:"voted_at"`
	CreatedAt time.Time `json:"created_at"`
}
