package database

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"

	"hopenbackend/pkg/config"
)

// Connect establishes a connection pool to PostgreSQL
func Connect(cfg config.PostgreSQLConfig) (*pgxpool.Pool, error) {
	connString := fmt.Sprintf(
		"postgres://%s:%s@%s:%d/%s?sslmode=%s",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		cfg.SSLMode,
	)

	// Parse connection string and configure pool
	poolConfig, err := pgxpool.ParseConfig(connString)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	// Configure connection pool
	poolConfig.MaxConns = cfg.MaxConnections
	poolConfig.MinConns = cfg.MinConnections
	poolConfig.MaxConnLifetime = cfg.MaxConnectionLifetime
	poolConfig.MaxConnIdleTime = cfg.MaxConnectionIdleTime

	// Configure connection settings
	poolConfig.ConnConfig.ConnectTimeout = 10 * time.Second
	poolConfig.ConnConfig.RuntimeParams = map[string]string{
		"application_name": "hopen-backend",
		"timezone":         "UTC",
	}

	// Create connection pool
	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return pool, nil
}

// HealthCheck performs a database health check
func HealthCheck(pool *pgxpool.Pool) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var result int
	err := pool.QueryRow(ctx, "SELECT 1").Scan(&result)
	if err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	return nil
}

// Migration represents a database migration
type Migration struct {
	Version int64
	Name    string
	SQL     string
}

// Migrate runs database migrations
func Migrate(pool *pgxpool.Pool, migrations []Migration) error {
	ctx := context.Background()
	
	// Create migrations table if it doesn't exist
	_, err := pool.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS schema_migrations (
			version BIGINT PRIMARY KEY,
			dirty BOOLEAN NOT NULL DEFAULT FALSE,
			applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create migrations table: %w", err)
	}

	// Run migrations
	for _, migration := range migrations {
		if err := runMigration(ctx, pool, migration); err != nil {
			return fmt.Errorf("failed to run migration %d: %w", migration.Version, err)
		}
	}

	return nil
}

func runMigration(ctx context.Context, pool *pgxpool.Pool, migration Migration) error {
	// Check if migration already applied
	var count int
	err := pool.QueryRow(ctx, "SELECT COUNT(*) FROM schema_migrations WHERE version = $1", migration.Version).Scan(&count)
	if err != nil {
		return fmt.Errorf("failed to check migration status: %w", err)
	}

	if count > 0 {
		return nil // Migration already applied
	}

	// Begin transaction
	tx, err := pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Execute migration
	_, err = tx.Exec(ctx, migration.SQL)
	if err != nil {
		return fmt.Errorf("failed to execute migration SQL: %w", err)
	}

	// Record migration
	_, err = tx.Exec(ctx, "INSERT INTO schema_migrations (version) VALUES ($1)", migration.Version)
	if err != nil {
		return fmt.Errorf("failed to record migration: %w", err)
	}

	// Commit transaction
	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit migration: %w", err)
	}

	return nil
}

// GetBaseMigrations returns the base database schema migrations
func GetBaseMigrations() []Migration {
	return []Migration{
		{
			Version: 1,
			Name:    "create_users_table",
			SQL: `
				CREATE TABLE IF NOT EXISTS users (
					id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
					email VARCHAR(255) UNIQUE NOT NULL,
					username VARCHAR(50) UNIQUE,
					first_name VARCHAR(100),
					last_name VARCHAR(100),
					avatar_url TEXT,
					bio TEXT,
					phone VARCHAR(20),
					date_of_birth DATE,
					gender VARCHAR(20),
					location VARCHAR(255),
					is_verified BOOLEAN DEFAULT FALSE,
					is_active BOOLEAN DEFAULT TRUE,
					last_seen_at TIMESTAMP WITH TIME ZONE,
					created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
					updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
				);

				CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
				CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
				CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
			`,
		},
		{
			Version: 2,
			Name:    "create_bubbles_table",
			SQL: `
				CREATE TABLE IF NOT EXISTS bubbles (
					id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
					creator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
					name VARCHAR(100) NOT NULL,
					capacity INTEGER NOT NULL DEFAULT 5 CHECK (capacity >= 2 AND capacity <= 5),
					current_members INTEGER DEFAULT 1 CHECK (current_members >= 0),
					status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'archived')),
					expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
					created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
					updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
				);

				CREATE INDEX IF NOT EXISTS idx_bubbles_creator ON bubbles(creator_id);
				CREATE INDEX IF NOT EXISTS idx_bubbles_status ON bubbles(status);
				CREATE INDEX IF NOT EXISTS idx_bubbles_expires_at ON bubbles(expires_at);
			`,
		},
		{
			Version: 3,
			Name:    "create_bubble_members_table",
			SQL: `
				CREATE TABLE IF NOT EXISTS bubble_members (
					id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
					bubble_id UUID NOT NULL REFERENCES bubbles(id) ON DELETE CASCADE,
					user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
					status VARCHAR(20) DEFAULT 'active',
					joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
					left_at TIMESTAMP WITH TIME ZONE,
					UNIQUE(bubble_id, user_id)
				);

				CREATE INDEX IF NOT EXISTS idx_bubble_members_bubble_id ON bubble_members(bubble_id);
				CREATE INDEX IF NOT EXISTS idx_bubble_members_user_id ON bubble_members(user_id);
				CREATE INDEX IF NOT EXISTS idx_bubble_members_status ON bubble_members(status);
			`,
		},
		{
			Version: 4,
			Name:    "create_friendships_table",
			SQL: `
				CREATE TABLE IF NOT EXISTS friendships (
					id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
					requester_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
					addressee_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
					status VARCHAR(20) DEFAULT 'pending',
					created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
					updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
					UNIQUE(requester_id, addressee_id),
					CHECK (requester_id != addressee_id)
				);

				CREATE INDEX IF NOT EXISTS idx_friendships_requester_id ON friendships(requester_id);
				CREATE INDEX IF NOT EXISTS idx_friendships_addressee_id ON friendships(addressee_id);
				CREATE INDEX IF NOT EXISTS idx_friendships_status ON friendships(status);
			`,
		},
		{
			Version: 5,
			Name:    "create_messages_table",
			SQL: `
				CREATE TABLE IF NOT EXISTS messages (
					id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
					bubble_id UUID REFERENCES bubbles(id) ON DELETE CASCADE,
					sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
					content TEXT NOT NULL,
					message_type VARCHAR(20) DEFAULT 'text',
					media_url TEXT,
					reply_to_id UUID REFERENCES messages(id),
					is_edited BOOLEAN DEFAULT FALSE,
					is_deleted BOOLEAN DEFAULT FALSE,
					created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
					updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
				);

				CREATE INDEX IF NOT EXISTS idx_messages_bubble_id ON messages(bubble_id);
				CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
				CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
				CREATE INDEX IF NOT EXISTS idx_messages_reply_to_id ON messages(reply_to_id);
			`,
		},
		{
			Version: 6,
			Name:    "create_notifications_table",
			SQL: `
				CREATE TABLE IF NOT EXISTS notifications (
					id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
					user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
					type VARCHAR(50) NOT NULL,
					title VARCHAR(255) NOT NULL,
					body TEXT NOT NULL,
					data JSONB,
					is_read BOOLEAN DEFAULT FALSE,
					created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
				);

				CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
				CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
				CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
				CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
			`,
		},
		{
			Version: 7,
			Name:    "create_media_files_table",
			SQL: `
				CREATE TABLE IF NOT EXISTS media_files (
					id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
					user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
					filename VARCHAR(255) NOT NULL,
					original_filename VARCHAR(255) NOT NULL,
					content_type VARCHAR(100) NOT NULL,
					size_bytes BIGINT NOT NULL,
					storage_path TEXT NOT NULL,
					storage_bucket VARCHAR(100) NOT NULL,
					is_public BOOLEAN DEFAULT FALSE,
					metadata JSONB,
					created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
				);

				CREATE INDEX IF NOT EXISTS idx_media_files_user_id ON media_files(user_id);
				CREATE INDEX IF NOT EXISTS idx_media_files_content_type ON media_files(content_type);
				CREATE INDEX IF NOT EXISTS idx_media_files_created_at ON media_files(created_at);
			`,
		},
	}
}
