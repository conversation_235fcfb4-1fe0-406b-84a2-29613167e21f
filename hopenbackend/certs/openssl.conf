[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = CA
L = San Francisco
O = Hopen
OU = Development
CN = *********

[v3_req]
basicConstraints = CA:FALSE
keyUsage = digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = hopen.local
DNS.4 = *.hopen.local
DNS.5 = hopen_backend
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = *********
IP.4 = ************ 