-- Migration: Enhance contacts and friendships tables
-- Add missing columns and constraints for better functionality

-- Add missing columns to contacts table if they don't exist
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS message TEXT;
ALTER TABLE contacts ADD COLUMN IF NOT EXISTS accepted_at TIMESTAMP WITH TIME ZONE;

-- Add missing columns to friendships table if they don't exist  
ALTER TABLE friendships ADD COLUMN IF NOT EXISTS source_bubble_id UUID REFERENCES bubbles(id) ON DELETE SET NULL;

-- Add missing columns to friend_requests table if they don't exist
ALTER TABLE friend_requests ADD COLUMN IF NOT EXISTS source_bubble_id UUID REFERENCES bubbles(id) ON DELETE SET NULL;
ALTER TABLE friend_requests ADD COLUMN IF NOT EXISTS auto_generated BOOLEAN DEFAULT TRUE;

-- Add additional indexes for better performance
CREATE INDEX IF NOT EXISTS idx_contacts_message ON contacts(message) WHERE message IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_contacts_accepted_at ON contacts(accepted_at);
CREATE INDEX IF NOT EXISTS idx_contacts_relationship ON contacts(user_id, contact_user_id, is_favorite);

CREATE INDEX IF NOT EXISTS idx_friendships_source_bubble_id ON friendships(source_bubble_id);
CREATE INDEX IF NOT EXISTS idx_friendships_created_at ON friendships(created_at);

CREATE INDEX IF NOT EXISTS idx_friend_requests_source_bubble_id ON friend_requests(source_bubble_id);
CREATE INDEX IF NOT EXISTS idx_friend_requests_auto_generated ON friend_requests(auto_generated);

-- Add comments for documentation
COMMENT ON COLUMN contacts.message IS 'Optional message with contact request';
COMMENT ON COLUMN contacts.accepted_at IS 'When the contact request was accepted';
COMMENT ON COLUMN friendships.source_bubble_id IS 'Bubble that expired to create this friendship';
COMMENT ON COLUMN friend_requests.source_bubble_id IS 'Bubble expiry that triggered this friend request';
COMMENT ON COLUMN friend_requests.auto_generated IS 'Whether this was auto-generated from bubble expiry';
