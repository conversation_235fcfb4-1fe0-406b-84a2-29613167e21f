-- Enhance users table with best-practice fields and constraints
-- Migration: 000007_enhance_users_table_best_practices.up.sql

-- Add new columns
ALTER TABLE users
    ADD COLUMN IF NOT EXISTS location VARCHAR(255),
    ADD COLUMN IF NOT EXISTS timezone VARCHAR(50),
    ADD COLUMN IF NOT EXISTS language VARCHAR(10) DEFAULT 'en',
    ADD COLUMN IF NOT EXISTS gender VARCHAR(20),
    ADD COLUMN IF NOT EXISTS website VARCHAR(500),
    ADD COLUMN IF NOT EXISTS social_links JSONB DEFAULT '{}',
    ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}',
    ADD COLUMN IF NOT EXISTS last_seen_at TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS login_count INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS email_notifications_enabled BOOLEAN DEFAULT TRUE,
    ADD COLUMN IF NOT EXISTS push_notifications_enabled BOOLEAN DEFAULT TRUE,
    ADD COLUMN IF NOT EXISTS profile_visibility VARCHAR(20) DEFAULT 'public' CHECK (profile_visibility IN ('public', 'friends', 'private')),
    ADD COLUMN IF NOT EXISTS searchable BOOLEAN DEFAULT TRUE,
    ADD COLUMN IF NOT EXISTS total_bubbles_joined INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS total_friends INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS total_contacts INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS engagement_score DECIMAL(5,2) DEFAULT 0.00,
    ADD COLUMN IF NOT EXISTS last_engagement_at TIMESTAMP WITH TIME ZONE;

-- Add recommended data validation constraints (no IF NOT EXISTS)
ALTER TABLE users ADD CONSTRAINT chk_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');
ALTER TABLE users ADD CONSTRAINT chk_username_format 
    CHECK (username ~* '^[a-zA-Z0-9_]{3,50}$');
ALTER TABLE users ADD CONSTRAINT chk_phone_format 
    CHECK (phone_number IS NULL OR phone_number ~* '^\+?[1-9]\d{1,14}$');
ALTER TABLE users ADD CONSTRAINT chk_date_of_birth 
    CHECK (date_of_birth IS NULL OR date_of_birth <= CURRENT_DATE - INTERVAL '13 years'); 