-- Fix bubble_requests table schema to match backend expectations
-- This migration adds missing request types and fields needed for the voting system

-- Add request_type column if it doesn't exist
ALTER TABLE bubble_requests ADD COLUMN IF NOT EXISTS request_type VARCHAR(20) NOT NULL DEFAULT 'join';

-- Add constraint for request types
ALTER TABLE bubble_requests ADD CONSTRAINT bubble_requests_request_type_check 
    CHECK (request_type IN ('join', 'invite', 'propose', 'kick', 'start'));

-- Make bubble_id nullable for start requests (no bubble exists yet)
ALTER TABLE bubble_requests ALTER COLUMN bubble_id DROP NOT NULL;

-- Add recipient_id column if it doesn't exist
ALTER TABLE bubble_requests ADD COLUMN IF NOT EXISTS recipient_id UUID REFERENCES users(id) ON DELETE CASCADE;

-- Add missing columns if they don't exist
ALTER TABLE bubble_requests
    ADD COLUMN IF NOT EXISTS target_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    ADD COLUMN IF NOT EXISTS requires_unanimous BOOLEAN DEFAULT true,
    ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- Update status constraint to include 'approved' and 'rejected' (backend expects these)
ALTER TABLE bubble_requests DROP CONSTRAINT IF EXISTS bubble_requests_status_check;
ALTER TABLE bubble_requests ADD CONSTRAINT bubble_requests_status_check 
    CHECK (status IN ('pending', 'accepted', 'declined', 'expired', 'approved', 'rejected'));

-- Create request_votes table for tracking individual member votes on requests
CREATE TABLE request_votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_id UUID NOT NULL REFERENCES bubble_requests(id) ON DELETE CASCADE,
    voter_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    vote VARCHAR(10) NOT NULL CHECK (vote IN ('approve', 'reject')),
    voted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(request_id, voter_id)
);

-- Add indexes for the new columns and table
CREATE INDEX idx_bubble_requests_request_type ON bubble_requests(request_type);
CREATE INDEX idx_bubble_requests_recipient_id ON bubble_requests(recipient_id);
CREATE INDEX idx_bubble_requests_target_user_id ON bubble_requests(target_user_id);
CREATE INDEX idx_bubble_requests_requires_unanimous ON bubble_requests(requires_unanimous);
CREATE INDEX idx_bubble_requests_completed_at ON bubble_requests(completed_at);

-- Request votes indexes
CREATE INDEX idx_request_votes_request_id ON request_votes(request_id);
CREATE INDEX idx_request_votes_voter_id ON request_votes(voter_id);
CREATE INDEX idx_request_votes_vote ON request_votes(vote);
CREATE INDEX idx_request_votes_voted_at ON request_votes(voted_at);

-- Add comments for documentation
COMMENT ON TABLE bubble_requests IS 'All bubble-related requests: join, invite, propose, kick, start';
COMMENT ON TABLE request_votes IS 'Individual member votes on bubble requests requiring consensus';
COMMENT ON COLUMN bubble_requests.request_type IS 'Type of request: join, invite, propose, kick, start';
COMMENT ON COLUMN bubble_requests.recipient_id IS 'User receiving the request (for invites)';
COMMENT ON COLUMN bubble_requests.target_user_id IS 'User being invited/kicked (null for join/start requests)';
COMMENT ON COLUMN bubble_requests.requires_unanimous IS 'Whether all members must approve this request';
COMMENT ON COLUMN bubble_requests.completed_at IS 'When the request was completed (approved/rejected)';
