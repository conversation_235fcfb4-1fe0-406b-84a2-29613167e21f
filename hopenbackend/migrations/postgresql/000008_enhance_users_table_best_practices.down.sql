-- Rollback: Remove best-practice fields and constraints from users table
-- Migration: 000007_enhance_users_table_best_practices.down.sql

-- Drop constraints
ALTER TABLE users
    DROP CONSTRAINT IF EXISTS chk_email_format,
    DROP CONSTRAINT IF EXISTS chk_username_format,
    DROP CONSTRAINT IF EXISTS chk_phone_format,
    DROP CONSTRAINT IF EXISTS chk_date_of_birth;

-- Drop columns
ALTER TABLE users
    DROP COLUMN IF EXISTS location,
    DROP COLUMN IF EXISTS timezone,
    DROP COLUMN IF EXISTS language,
    DROP COLUMN IF EXISTS gender,
    DROP COLUMN IF EXISTS website,
    DROP COLUMN IF EXISTS social_links,
    DROP COLUMN IF EXISTS preferences,
    DROP COLUMN IF EXISTS last_seen_at,
    DROP COLUMN IF EXISTS login_count,
    DROP COLUMN IF EXISTS last_login_at,
    DROP COLUMN IF EXISTS two_factor_enabled,
    DROP COLUMN IF EXISTS email_notifications_enabled,
    DROP COLUMN IF EXISTS push_notifications_enabled,
    DROP COLUMN IF EXISTS profile_visibility,
    DROP COLUMN IF EXISTS searchable,
    DROP COLUMN IF EXISTS total_bubbles_joined,
    DROP COLUMN IF EXISTS total_friends,
    DROP COLUMN IF EXISTS total_contacts,
    DROP COLUMN IF EXISTS engagement_score,
    DROP COLUMN IF EXISTS last_engagement_at; 