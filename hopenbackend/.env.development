# Development Environment Configuration
ENVIRONMENT=development
DOCKER_HOST_IP=*********

# TLS Configuration
ENABLE_HTTPS=true
ENABLE_HTTP3=true
SSL_CERT_PATH=/app/certs/server.crt
SSL_KEY_PATH=/app/certs/server.key
TLS_DOMAIN=hopen.local

# API Configuration
API_BASE_URL=https://hopen.local:8443
BACKEND_URL=https://hopen.local:8443

# Database Configuration
POSTGRES_HOST=postgresql
POSTGRES_PORT=5432
POSTGRES_DB=hopen_db
POSTGRES_USER=hopen
POSTGRES_PASSWORD=hopen123

# Redis/Valkey Configuration
VALKEY_URL=redis://valkey:6379

# MQTT Configuration
MQTT_BROKER_URL=emqx
MQTT_PORT=1883
MQTT_SECURE_PORT=8883

# MinIO Configuration
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=hopen
MINIO_SECRET_KEY=hopen123
MINIO_BUCKET=hopen-storage
MINIO_USE_SSL=false

# Ory Stack Configuration
ORY_KRATOS_PUBLIC_URL=http://kratos:4433
ORY_KRATOS_ADMIN_URL=http://kratos:4434
ORY_HYDRA_PUBLIC_URL=http://hydra:4444
ORY_HYDRA_ADMIN_URL=http://hydra:4445
