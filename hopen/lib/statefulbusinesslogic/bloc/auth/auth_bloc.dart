import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// import '../../../repositories/usecase/login_usecase.dart';

// Placeholder for actual repository path post-refactor
import '../../../repositories/auth/auth_repository.dart';
// Current path for UserModel, same as in auth_event.dart
import '../../core/services/logging_service.dart';
// import '../../../statefulbusinesslogic/core/models/user_model.dart'
//     as core_models; // Keep this commented or remove if UserModel above is sufficient

// Current path for LoginUseCase
import '../../core/usecases/login_usecase.dart';
// Import for comprehensive data clearing on logout
import '../../core/services/user_data_isolation_service.dart';

import 'auth_event.dart';
import 'auth_state.dart';
import '../../core/services/deferred_profile_picture_service.dart';
import '../../../provider/services/presence/enhanced_presence_manager.dart';
import '../../../di/injection_container_refactored.dart' as di;

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc({
    required LoginUseCase loginUseCase,
    required AuthRepository authRepository,
    required DeferredProfilePictureService deferredProfilePictureService,
  }) : _loginUseCase = loginUseCase,
       _authRepository = authRepository,
       _deferredProfilePictureService = deferredProfilePictureService,
       super(const AuthState.initial()) {
    on<CheckAuthStatusEvent>(_onCheckAuthStatus);
    on<LoginEvent>(_onLogin);
    on<LoginWithGoogleEvent>(_onLoginWithGoogle);
    on<LoginWithAppleEvent>(_onLoginWithApple);
    on<LogoutEvent>(_onLogout);
    on<UserLoggedInEvent>(_onUserLoggedIn);
    on<MarkOnboardingCompleteEvent>(_onMarkOnboardingComplete);
    on<UpdateUserInfoEvent>(_onUpdateUserInfo);
    on<SendPasswordResetEmailEvent>(_onSendPasswordResetEmail);
    on<SignInWithEmailAndPasswordEvent>(_onSignInWithEmailAndPassword);
    on<SignUpWithEmailAndPasswordEvent>(_onSignUpWithEmailAndPassword);
    on<SignOutEvent>(_onSignOut);
    on<AuthenticationInconsistencyDetected>(
      _onAuthenticationInconsistencyDetected,
    );

    // TODO: Listen to auth state changes through repository
  }
  final LoginUseCase _loginUseCase;
  final AuthRepository _authRepository;
  final DeferredProfilePictureService _deferredProfilePictureService;

  Future<void> _onCheckAuthStatus(
    CheckAuthStatusEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    try {
      print(
        '🔍 AuthBloc._onCheckAuthStatus: Using AuthRepository to check auth status',
      );
      final result = await _authRepository.getCurrentUser();

      result.fold(
        onFailure: (failure) {
          print('🔍 AuthBloc._onCheckAuthStatus: User is not authenticated');
          emit(state.copyWith(status: AuthStatus.unauthenticated));
        },
        onSuccess: (user) {
          print(
            '🔍 AuthBloc._onCheckAuthStatus: User is authenticated: ${user.email}',
          );
          emit(
            state.copyWith(
              status: AuthStatus.authenticated,
              userId: user.id,
              email: user.email,
              firstName: user.firstName ?? '',
            ),
          );
          // Initialize presence service for real-time status
          print(
            '🔥 AuthBloc._onCheckAuthStatus: About to call _initializePresenceService with userId: ${user.id}',
          );
          _initializePresenceService(user.id);
          print(
            '🔥 AuthBloc._onCheckAuthStatus: Called _initializePresenceService',
          );
          // Check for deferred profile picture uploads
          _handleDeferredProfilePictureUpload();
        },
      );
    } catch (e) {
      print('🔍 AuthBloc._onCheckAuthStatus: Error checking auth status: $e');
      emit(state.copyWith(status: AuthStatus.unauthenticated));
    }
  }

  Future<void> _onLogin(LoginEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));

    try {
      print('🔍 AuthBloc._onLogin: Using AuthRepository for login');
      final result = await _authRepository.login(
        email: event.email,
        password: event.password,
      );

      result.fold(
        onFailure: (failure) {
          print('🔍 AuthBloc._onLogin: Login failed - ${failure.userMessage}');
          emit(
            state.copyWith(
              status: AuthStatus.error,
              errorMessage: failure.userMessage,
            ),
          );
        },
        onSuccess: (user) {
          print('🔍 AuthBloc._onLogin: Login successful, user: ${user.email}');
          emit(
            state.copyWith(
              status: AuthStatus.authenticated,
              userId: user.id,
              email: user.email,
              firstName: user.firstName ?? '',
            ),
          );
          // Initialize presence service for real-time status
          _initializePresenceService(user.id);
          // Check for deferred profile picture uploads
          _handleDeferredProfilePictureUpload();
        },
      );
    } catch (e) {
      print('🔍 AuthBloc._onLogin: Login failed with error: $e');
      emit(
        state.copyWith(
          status: AuthStatus.error,
          errorMessage: 'Login failed: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoginWithGoogle(
    LoginWithGoogleEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));
    final result = await _authRepository.loginWithGoogle();
    if (result.isSuccess) {
      emit(
        state.copyWith(
          status: AuthStatus.authenticated,
          userId: result.data.id,
          email: result.data.email,
          firstName: result.data.firstName,
        ),
      );
      // Initialize presence service for real-time status
      _initializePresenceService(result.data.id);
    } else {
      emit(
        state.copyWith(
          status: AuthStatus.error,
          errorMessage: result.error.userMessage,
        ),
      );
    }
  }

  Future<void> _onLoginWithApple(
    LoginWithAppleEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));
    final result = await _authRepository.loginWithApple();
    if (result.isSuccess) {
      emit(
        state.copyWith(
          status: AuthStatus.authenticated,
          userId: result.data.id,
          email: result.data.email,
          firstName: result.data.firstName,
        ),
      );
      // Initialize presence service for real-time status
      _initializePresenceService(result.data.id);
    } else {
      emit(
        state.copyWith(
          status: AuthStatus.error,
          errorMessage: result.error.userMessage,
        ),
      );
    }
  }

  Future<void> _onLogout(LogoutEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));

    try {
      print('🔍 AuthBloc._onLogout: Using AuthRepository for logout');
      await _authRepository.logout();
      print('🔍 AuthBloc._onLogout: Logout successful');
    } catch (e) {
      print('🔍 AuthBloc._onLogout: Logout error: $e');
      // Continue with logout even if remote logout fails
    }

    // Clear all cached user data to prevent data leakage between users
    // This implements OWASP security best practices for session management
    try {
      print(
        '🔍 AuthBloc._onLogout: Clearing all user data for security isolation',
      );
      await UserDataIsolationService.clearAllUserData(reason: 'User logout');
      print('🔍 AuthBloc._onLogout: All user data cleared successfully');
    } catch (e) {
      print('🔍 AuthBloc._onLogout: Error clearing user data: $e');
      // Continue with logout even if cache clearing fails to ensure user can still log out
    }

    // Always emit unauthenticated state regardless of remote logout result
    emit(const AuthState.unauthenticated());
  }

  Future<void> _onUserLoggedIn(
    UserLoggedInEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(
      state.copyWith(
        status: AuthStatus.authenticated,
        userId: event.user.id,
        email: event.user.email,
        firstName: event.user.firstName,
      ),
    );
  }

  Future<void> _onMarkOnboardingComplete(
    MarkOnboardingCompleteEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (state.status == AuthStatus.authenticated) {
      // Update local state immediately
      emit(state.copyWith(status: AuthStatus.authenticated));

      // Update backend asynchronously
      try {
        final result = await _authRepository.updateOnboardingStatus(true);
        if (result.isSuccess) {
          LoggingService.error(
            'Onboarding status updated successfully in backend',
          );
        } else {
          // Log error but don't revert state since user has completed onboarding
          LoggingService.error(
            'Failed to update onboarding status in backend: ${result.error.userMessage}',
          );
        }
      } on Exception catch (e) {
        // Log error but don't revert state
        LoggingService.error('Error updating onboarding status: $e');
      }
    }
  }

  Future<void> _onUpdateUserInfo(
    UpdateUserInfoEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (state.status == AuthStatus.authenticated) {
      emit(state.copyWith(firstName: event.firstName));
    }
  }

  Future<void> _onSendPasswordResetEmail(
    SendPasswordResetEmailEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      // Use the Ory auth service to initiate password reset
      // For now, simulate password reset since we don't have the specific method
      // In a real implementation, you'd add initiatePasswordReset to AuthRepository

      // Simulate successful password reset
      emit(state.copyWith(status: AuthStatus.passwordResetSent));
    } on Exception catch (e) {
      emit(
        state.copyWith(
          status: AuthStatus.error,
          errorMessage: 'Password reset failed: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onSignInWithEmailAndPassword(
    SignInWithEmailAndPasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      // Use the auth repository to sign in
      final result = await _authRepository.login(
        email: event.email,
        password: event.password,
      );

      if (result.isSuccess) {
        final user = result.data;
        emit(
          state.copyWith(
            status: AuthStatus.authenticated,
            userId: user.id,
            email: user.email,
            firstName: user.firstName,
          ),
        );
        // Initialize presence service for real-time status
        _initializePresenceService(user.id);
      } else {
        emit(
          state.copyWith(
            status: AuthStatus.error,
            errorMessage: result.error.userMessage,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          status: AuthStatus.error,
          errorMessage: 'Sign in failed: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onSignUpWithEmailAndPassword(
    SignUpWithEmailAndPasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      // Use the auth repository to sign up
      final result = await _authRepository.signUp(
        email: event.email,
        password: event.password,
        username: event.email.split('@')[0], // Use email prefix as username
        firstName: 'User', // Placeholder first name
        lastName: 'Name', // Placeholder last name
      );

      if (result.isSuccess) {
        final user = result.data;
        emit(
          state.copyWith(
            status: AuthStatus.authenticated,
            userId: user.id,
            email: user.email,
            firstName: user.firstName,
          ),
        );
        // Initialize presence service for real-time status
        _initializePresenceService(user.id);
      } else {
        emit(
          state.copyWith(
            status: AuthStatus.error,
            errorMessage: result.error.userMessage,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          status: AuthStatus.error,
          errorMessage: 'Sign up failed: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onSignOut(SignOutEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      await _authRepository.logout();
      emit(const AuthState.unauthenticated());
    } on Exception catch (e) {
      emit(
        state.copyWith(
          status: AuthStatus.error,
          errorMessage: 'Sign out failed: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onAuthenticationInconsistencyDetected(
    AuthenticationInconsistencyDetected event,
    Emitter<AuthState> emit,
  ) async {
    LoggingService.error(
      'Authentication inconsistency detected: ${event.details}',
    );
    // Force sign-out to resolve inconsistency
    await _onSignOut(const SignOutEvent(), emit);
  }

  /// Initialize presence service when user becomes authenticated
  void _initializePresenceService(String userId) {
    try {
      print('🔥 AuthBloc: Initializing presence service for user: $userId');
      LoggingService.info(
        'AuthBloc: Initializing presence service for user: $userId',
      );
      final enhancedPresenceManager = di.sl<EnhancedPresenceManager>();
      enhancedPresenceManager
          .initialize(userId)
          .then((_) {
            print(
              '🔥 AuthBloc: Enhanced presence manager initialized successfully for user: $userId',
            );
            LoggingService.success(
              'AuthBloc: Enhanced presence manager initialized successfully for user: $userId',
            );
          })
          .catchError((Object error) {
            print(
              '🔥 AuthBloc: Failed to initialize enhanced presence manager: $error',
            );
            LoggingService.error(
              'AuthBloc: Failed to initialize enhanced presence manager: $error',
            );
          });
    } catch (e) {
      print('🔥 AuthBloc: Error getting presence service from DI: $e');
      LoggingService.error(
        'AuthBloc: Error getting presence service from DI: $e',
      );
    }
  }

  /// Trigger contact request checking when user becomes authenticated
  void _triggerContactRequestCheck() {
    // Use a short delay to ensure the UI is ready
    Future.delayed(const Duration(milliseconds: 500), () {
      // We need a BuildContext to show the dialog, but we don't have one in the BLoC
      // Instead, we'll emit an event that the UI can listen to
      LoggingService.info(
        'AuthBloc: User authenticated, contact request checking should be triggered by UI',
      );
    });
  }

  /// Handle deferred profile picture upload after successful authentication
  void _handleDeferredProfilePictureUpload() {
    print('🔍 AuthBloc: _handleDeferredProfilePictureUpload called');
    // Run in background without blocking authentication flow
    Future.microtask(() async {
      try {
        print('🔍 AuthBloc: Checking for deferred profile picture...');
        final hasDeferredPicture = await _deferredProfilePictureService.hasDeferredProfilePicture();
        print('🔍 AuthBloc: hasDeferredProfilePicture result: $hasDeferredPicture');

        if (hasDeferredPicture) {
          print(
            '📸 AuthBloc: Found deferred profile picture, attempting upload...',
          );
          final uploadedUrl =
              await _deferredProfilePictureService
                  .uploadDeferredProfilePicture();
          if (uploadedUrl != null) {
            print(
              '✅ AuthBloc: Successfully uploaded deferred profile picture: $uploadedUrl',
            );
          } else {
            print('⚠️ AuthBloc: Failed to upload deferred profile picture');
          }
        } else {
          print('ℹ️ AuthBloc: No deferred profile picture found');
          // Debug: Get upload stats to see what's stored
          final stats = await _deferredProfilePictureService.getUploadStats();
          print('🔍 AuthBloc: Deferred upload stats: $stats');
        }
      } catch (e) {
        print('❌ AuthBloc: Error handling deferred profile picture upload: $e');
      }
    });
  }

  @override
  Future<void> close() async {
    // No subscriptions to cancel anymore
    return super.close();
  }
}
