import 'dart:io'; // For File

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../statefulbusinesslogic/bloc/profile_picture/profile_picture_bloc.dart';
import '../../../../../statefulbusinesslogic/bloc/profile_picture/profile_picture_event.dart';
import '../../../../../statefulbusinesslogic/bloc/profile_picture/profile_picture_state.dart';
import '../../../../widgets/custom_toast.dart';
import '../../../../widgets/profile_picture_widget.dart';
import '../signup_step_base.dart';

class Step5ProfilePicturePage extends SignupStepBase {
  Step5ProfilePicturePage({
    required super.currentStep,
    required super.totalSteps,
    required this.onNextCustom,
    required VoidCallback super.onBack,
    this.initialImageUrl,
    super.key,
  }) : super(title: 'Choose your profile picture', onNext: () {});
  final void Function(String?) onNextCustom;
  final String? initialImageUrl;

  @override
  State<Step5ProfilePicturePage> createState() =>
      _Step5ProfilePicturePageState();
}

class _Step5ProfilePicturePageState
    extends SignupStepBaseState<Step5ProfilePicturePage> {
  File? _selectedImageFile;
  String? _processedImagePath;
  bool _isProcessingImage = false;

  @override
  void initState() {
    super.initState();

    // Restore previously selected image (if provided)
    _processedImagePath = widget.initialImageUrl;
  }

  @override
  bool isFormValid() => true;

  // This handleNext is called by the button in SignupStepBase
  @override
  void handleNext() {
    if (isFormValid()) {
      // Pass the processed image path (local file path for signup)
      widget.onNextCustom(_processedImagePath);
    } else {
      // Show custom toast when validation fails
      CustomToast.showError(context, 'Please enter all your infos to continue');
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ProfilePictureBloc, ProfilePictureState>(
      listener: (context, state) {
        if (state is ProfilePictureProcessedLocally) {
          setState(() {
            _processedImagePath = state.localPath;
            _selectedImageFile = File(state.localPath);
            _isProcessingImage = false;
          });
          if (state.message != null) {
            CustomToast.showSuccess(context, state.message!);
          }
        } else if (state is ProfilePictureError) {
          setState(() {
            _isProcessingImage = false;
          });
          CustomToast.showError(context, state.message);
        } else if (state is ProfilePictureCancelled) {
          setState(() {
            _isProcessingImage = false;
          });
        } else if (state is ProfilePictureLoading) {
          setState(() {
            _isProcessingImage = true;
          });
        }
      },
      child: super.build(context),
    );
  }

  @override
  List<Widget> buildStepContent(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final spacingHeight = screenHeight / 48;
    final fieldHeight = screenHeight / 16;
    final avatarSize = screenHeight * 0.2; // Match profile picture dialog size

    return [
      // Image requirements text – identical style and spacing to Step 4 notice
      Text(
        'It should be at least 640x640 pixels, in JPEG, PNG, WebP, HEIF or HEIC format.',
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.7),
          fontSize: 16,
          height: 1.5,
        ),
        textAlign: TextAlign.center,
      ),
      SizedBox(height: spacingHeight * 2),
      Center(
        child: GestureDetector(
          onTap: null,
          child:
              _processedImagePath != null || _selectedImageFile != null
                  ? ProfilePictureWidget(
                    imageUrl: _processedImagePath ?? _selectedImageFile?.path,
                    firstName: 'User',
                    lastName: 'Avatar',
                    radius: avatarSize / 2,
                  )
                  : Container(
                    width: avatarSize,
                    height: avatarSize,
                    decoration: ShapeDecoration(
                      shape: RoundedSuperellipseBorder(
                        borderRadius: BorderRadius.circular(avatarSize * 0.4),
                        side: BorderSide(color: Colors.blue, width: 2),
                      ),
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                    child: Icon(
                      Icons.add_a_photo,
                      color: Colors.white,
                      size: avatarSize * 0.4,
                    ),
                  ),
        ),
      ),
      SizedBox(height: spacingHeight),
      SizedBox(
        height: fieldHeight,
        child: Row(
          children: [
            Expanded(
              child: _buildButton(
                label: 'Gallery',
                iconWidget: SvgPicture.asset(
                  'assets/icons/image-1.svg',
                  width: 24,
                  height: 24,
                  colorFilter: ColorFilter.mode(Colors.white, BlendMode.srcIn),
                ),
                onPressed: _pickFromGallery,
              ),
            ),
            SizedBox(width: spacingHeight),
            Expanded(
              child: _buildButton(
                label: 'Camera',
                iconWidget: SvgPicture.asset(
                  'assets/icons/camera.svg',
                  width: 24,
                  height: 24,
                  colorFilter: ColorFilter.mode(Colors.white, BlendMode.srcIn),
                ),
                onPressed: _takePhoto,
              ),
            ),
          ],
        ),
      ),
      SizedBox(height: spacingHeight),
      // Skip for now text - matching Step 4 "Resend verification code" styling exactly
      TextButton(
        onPressed:
            () => widget.onNextCustom(null), // Call custom onNext for skip
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          alignment: Alignment.center,
        ),
        child: Text(
          'Skip for now',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 16,
          ),
        ),
      ),
    ];
  }

  Widget _buildButton({
    required String label,
    Widget? iconWidget,
    required VoidCallback? onPressed,
  }) {
    final screenHeight = MediaQuery.of(context).size.height;
    final fieldHeight = screenHeight / 16;

    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withValues(alpha: 0.1),
        foregroundColor: Colors.white,
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
          side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
        padding: EdgeInsets.zero,
        minimumSize: Size.zero,
        maximumSize: const Size(double.infinity, double.infinity),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        fixedSize: Size.fromHeight(fieldHeight),
      ),
      icon: iconWidget ?? const SizedBox.shrink(),
      label: Text(label),
    );
  }

  void _pickFromGallery() {
    try {
      setState(() {
        _isProcessingImage = true;
      });

      print('🚀 Starting gallery picker for signup...');

      // Use ProfilePictureBloc for gallery selection and processing
      context.read<ProfilePictureBloc>().add(const PickFromGalleryLocalEvent());
    } catch (e) {
      print('❌ Failed to pick image from gallery during signup: $e');
      CustomToast.showError(
        context,
        'Failed to pick image from gallery: ${e.toString()}',
      );
      setState(() {
        _isProcessingImage = false;
      });
    }
  }

  void _takePhoto() {
    try {
      setState(() {
        _isProcessingImage = true;
      });

      print('🚀 Starting camera capture for signup...');

      // Use ProfilePictureBloc for camera capture and processing
      context.read<ProfilePictureBloc>().add(const TakePhotoLocalEvent());
    } catch (e) {
      print('❌ Failed to take photo during signup: $e');
      CustomToast.showError(
        context,
        'Failed to take photo: ${e.toString()}',
      );
      setState(() {
        _isProcessingImage = false;
      });
    }
  }

  void _removeImage() {
    setState(() {
      _processedImagePath = null;
      _selectedImageFile = null;
    });

    CustomToast.show(context, 'Profile picture removed');
  }
}
